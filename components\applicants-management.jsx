"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { api } from "@/lib/api"
import { toast } from "sonner"
import { Loader2, Users, Eye, CheckCircle, Star, User, Building, Calendar, Award, ChevronLeft, ChevronRight } from "lucide-react"
import { ConfirmationAlert } from "./confirmation-alert"

// Inline Badge component
const Badge = ({ variant = "default", className = "", children, ...props }) => {
  const baseClasses = "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
  
  const variantClasses = {
    default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
    secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
    destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
    outline: "text-foreground"
  }
  
  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${className}`} {...props}>
      {children}
    </div>
  )
}

// Helper function to calculate age from birth date
const calculateAge = (birthDate) => {
  if (!birthDate) return null
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }
  return age
}

// Helper function to get gender display text
const getGenderText = (gender) => {
  if (!gender) return 'არ არის მითითებული'
  switch (gender.toLowerCase()) {
    case 'male':
    case 'm':
      return 'მამრობითი'
    case 'female':
    case 'f':
      return 'მდედრობითი'
    default:
      return gender
  }
}

export function ApplicantsManagement({ isOpen, onOpenChange, nodeId, nodeLabel, embedded = false, onViewDetails = null, detailMode = false, selectedApplicant = null }) {
  const [applicants, setApplicants] = useState([])
  const [loading, setLoading] = useState(false)
  const [showDetailModal, setShowDetailModal] = useState(false)
  const [selectedApplicantState, setSelectedApplicantState] = useState(null)
  const [confirmingApplicant, setConfirmingApplicant] = useState(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalApplicants, setTotalApplicants] = useState(0)
  const [perPage, setPerPage] = useState(10)

  // Confirmation alert states
  const [showConfirmAlert, setShowConfirmAlert] = useState(false)
  const [pendingConfirmation, setPendingConfirmation] = useState(null)

  // Fetch applicants when modal opens or page changes
  useEffect(() => {
    if (isOpen && nodeId) {
      fetchApplicants()
    }
  }, [isOpen, nodeId, currentPage])

  

const fetchApplicants = async () => {
  setLoading(true)
  console.log('Starting fetchApplicants with params:', { nodeId, perPage, currentPage })
  try {
    const response = await api.network.getApplicants(nodeId, perPage, currentPage)
    console.log('Applicants API Response:', response)
    console.log('Response status:', response.status)

    if (response && response.data) {
      console.log('Response data exists:', response.data)
      // Extract data array - it's directly in response.data
      const applicantsData = response.data || []
      console.log('Applicants data extracted:', applicantsData)

      // Sort applicants by quality (excellent first, poor last)
      const sortedApplicants = applicantsData.sort((a, b) => {
        const qualityOrder = { excellent: 0, good: 1, average: 2, poor: 3 }
        const qualityA = getApplicantQuality(a)
        const qualityB = getApplicantQuality(b)
        return qualityOrder[qualityA] - qualityOrder[qualityB]
      })

      // Set applicants data
      setApplicants(sortedApplicants)
      console.log('Applicants set successfully:', sortedApplicants.length, 'items')

      // Set pagination data from meta object
      const meta = response.meta || {}
      setCurrentPage(meta.current_page || 1)
      setTotalPages(meta.last_page || 1)
      setTotalApplicants(meta.total || 0)
      setPerPage(meta.per_page || 10)

      console.log('Pagination state updated:', {
        currentPage: meta.current_page,
        totalPages: meta.last_page,
        totalApplicants: meta.total,
        perPage: meta.per_page
      })
    } else {
      console.log('No response data, setting empty state')
      // Fallback for empty response
      setApplicants([])
      setTotalApplicants(0)
      setTotalPages(1)
      setCurrentPage(1)
    }
  } catch (error) {
    console.error('Failed to fetch applicants:', error)
    console.error('Error details:', error.response || error.message || error)
    toast.error("აპლიკანტების ჩატვირთვა ვერ მოხერხდა")
    setApplicants([])
    setTotalApplicants(0)
    setTotalPages(1)
    setCurrentPage(1)
  } finally {
    console.log('fetchApplicants finally block - setting loading to false')
    setLoading(false)
  }
}



  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page)
    }
  }

  const handleConfirmApplicant = (applicantId, applicantName, applicant) => {
    const quality = getApplicantQuality(applicant)
    const qualityText = quality === 'excellent' ? '🌟 შესანიშნავი კანდიდატი!' :
                       quality === 'good' ? '👍 კარგი კანდიდატი' :
                       quality === 'average' ? '📊 საშუალო კანდიდატი' :
                       '⚠️ სუსტი კანდიდატი - ფრთხილად!'

    // Store pending confirmation data
    setPendingConfirmation({
      applicantId,
      applicantName,
      applicant,
      quality,
      qualityText
    })

    // Show confirmation alert
    setShowConfirmAlert(true)
  }

  const handleConfirmationConfirm = async () => {
    if (!pendingConfirmation) return

    const { applicantId, applicantName, applicant } = pendingConfirmation

    console.log('Full applicant object:', applicant)
    console.log('Available fields:', Object.keys(applicant))

    setConfirmingApplicant(applicantId)
    setShowConfirmAlert(false)

    try {
      // Use applicant.id as contract_id (this should be the contract ID from the API response)
      const contractId = applicant.id
      console.log('Using contract_id:', contractId)

      const response = await api.network.confirmApplicant(contractId)

      if (response.success) {
        const quality = getApplicantQuality(applicant)
        const successMessage = quality === 'excellent' ? `🎉🌟 შესანიშნავი! ${applicantName} წარმატებით დადასტურდა!` :
                               quality === 'good' ? `✅👍 კარგი არჩევანი! ${applicantName} წარმატებით დადასტურდა!` :
                               quality === 'average' ? `✅📊 ${applicantName} წარმატებით დადასტურდა!` :
                               `⚠️🔴 ${applicantName} დადასტურდა. იყავით ფრთხილად!`

        toast.success(successMessage)
        onOpenChange(true) // Pass true to indicate refresh is needed
        // Reload network data after successful confirmation
        window.dispatchEvent(new CustomEvent('networkDataUpdated'))
      } else {
        toast.error(response.message || "აპლიკანტის დადასტურება ვერ მოხერხდა")
      }
    } catch (error) {
      console.error('Failed to confirm applicant:', error)
      toast.error(error.message || "აპლიკანტის დადასტურება ვერ მოხერხდა")
    } finally {
      setConfirmingApplicant(null)
      setPendingConfirmation(null)
    }
  }

  const handleConfirmationCancel = () => {
    setShowConfirmAlert(false)
    setPendingConfirmation(null)
  }

  const handleViewDetails = (applicant) => {
    console.log('handleViewDetails called with:', applicant)
    console.log('onViewDetails callback:', onViewDetails)
    console.log('embedded mode:', embedded)

    if (onViewDetails) {
      // If embedded and has onViewDetails callback, use it
      console.log('Using onViewDetails callback')
      onViewDetails(applicant)
    } else {
      // Otherwise use internal modal
      console.log('Using internal modal')
      setSelectedApplicantState(applicant)
      setShowDetailModal(true)
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString('ka-GE', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch {
      return dateString
    }
  }

  const getPointsDistribution = (points) => {
    if (!Array.isArray(points) || points.length === 0) {
      return "ქულები არ არის"
    }

    const distribution = {}
    points.forEach(point => {
      const score = point.score || 0
      distribution[score] = (distribution[score] || 0) + 1
    })

    return Object.entries(distribution)
      .sort(([a], [b]) => parseInt(b) - parseInt(a))
      .map(([score, count]) => `${score} ქულა - ${count} ჯერ`)
      .join(', ')
  }

  const getApplicantDisplayName = (applicant) => {
    if (applicant.user?.first_name && applicant.user?.last_name) {
      return `${applicant.user.first_name} ${applicant.user.last_name}`
    }
    return `მომხმარებელი #${applicant.user?.id || applicant.worker_id}`
  }

  const getApplicantQuality = (applicant) => {
    const rate = applicant.user?.rate || 0
    const leaveCount = applicant.user?.leave_count || 0

    if (rate >= 8 && leaveCount === 0) return 'excellent'
    if (rate >= 6 && leaveCount <= 2) return 'good'
    if (rate >= 4 && leaveCount <= 5) return 'average'
    return 'poor'
  }

  if (embedded) {
    // Detail mode - show only applicant details
    if (detailMode && selectedApplicant) {
      return (
        <>
          

          <div className="space-y-6 animate-in fade-in-0 slide-in-from-right-4 duration-300">
          {/* Header with Confirmation Button */}
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold flex items-center gap-2 text-blue-800 dark:text-blue-200">
              <User className="w-5 h-5" />
              <span className="hidden sm:inline">აპლიკანტის დეტალები</span>
              <span className="sm:hidden">დეტალები</span>
            </h2>
            <Button
              onClick={() => handleConfirmApplicant(selectedApplicant.id, getApplicantDisplayName(selectedApplicant), selectedApplicant)}
              disabled={confirmingApplicant === selectedApplicant.id}
              className={`flex items-center gap-2 px-3 py-2 text-white font-semibold text-sm ${
                getApplicantQuality(selectedApplicant) === 'excellent' ? 'bg-green-600 hover:bg-green-700' :
                getApplicantQuality(selectedApplicant) === 'good' ? 'bg-blue-600 hover:bg-blue-700' :
                getApplicantQuality(selectedApplicant) === 'average' ? 'bg-yellow-600 hover:bg-yellow-700' :
                'bg-red-600 hover:bg-red-700'
              }`}
            >
              {confirmingApplicant === selectedApplicant.id ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span className="hidden sm:inline">დადასტურება...</span>
                  <span className="sm:hidden">...</span>
                </>
              ) : (
                <>
                  <CheckCircle className="w-4 h-4" />
                  <span className="hidden lg:inline">
                    {getApplicantQuality(selectedApplicant) === 'excellent' ? '🌟 შესანიშნავი კანდიდატი!' :
                     getApplicantQuality(selectedApplicant) === 'good' ? '👍 კარგი არჩევანი' :
                     getApplicantQuality(selectedApplicant) === 'average' ? '📊 დადასტურება' :
                     '⚠️ ფრთხილად - დადასტურება'}
                  </span>
                  <span className="lg:hidden">
                    {getApplicantQuality(selectedApplicant) === 'excellent' ? '🌟 შესანიშნავი!' :
                     getApplicantQuality(selectedApplicant) === 'good' ? '👍 კარგი' :
                     getApplicantQuality(selectedApplicant) === 'average' ? '📊 დადასტურება' :
                     '⚠️ ფრთხილად'}
                  </span>
                </>
              )}
            </Button>
          </div>

          {/* User Basic Info */}
          <div className="border rounded-lg p-6 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-blue-200 dark:border-blue-800">
            <h3 className="font-semibold mb-4 flex items-center gap-2 text-blue-800 dark:text-blue-200">
              <User className="w-5 h-5" />
              ძირითადი ინფორმაცია
            </h3>
            <div className="space-y-4">
              {/* Name with Age */}
              <div>
                <span className="text-sm text-muted-foreground">სახელი და ასაკი:</span>
                <p className="font-medium text-lg">
                  {getApplicantDisplayName(selectedApplicant)}
                  {selectedApplicant.user?.birth_date && (
                    <span className="text-muted-foreground ml-2">
                      {calculateAge(selectedApplicant.user.birth_date)} წლის
                    </span>
                  )}
                </p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <span className="text-sm text-muted-foreground">სქესი:</span>
                  <p className="font-medium">{getGenderText(selectedApplicant.user?.gender)}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">რეიტინგი:</span>
                  <div className="flex items-center gap-2 mt-1">
                    <Star className="w-4 h-4 text-yellow-500" />
                    {selectedApplicant.user?.rate ? (
                      <Badge
                        variant="outline"
                        className={`${
                          selectedApplicant.user.rate >= 8 ? 'border-green-500 text-green-700 bg-green-50' :
                          selectedApplicant.user.rate >= 6 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                          'border-red-500 text-red-700 bg-red-50'
                        }`}
                      >
                        {selectedApplicant.user.rate}/10
                      </Badge>
                    ) : (
                      <span className="font-medium text-muted-foreground">N/A</span>
                    )}
                  </div>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">დატოვების რაოდენობა:</span>
                  <div className="mt-1">
                    <Badge
                      variant="outline"
                      className={`${
                        (selectedApplicant.user?.leave_count || 0) === 0 ? 'border-green-500 text-green-700 bg-green-50' :
                        (selectedApplicant.user?.leave_count || 0) <= 2 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                        'border-red-500 text-red-700 bg-red-50'
                      }`}
                    >
                      {selectedApplicant.user?.leave_count || 0} ჯერ
                    </Badge>
                  </div>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">ქვეყანა:</span>
                  <p className="font-medium">{selectedApplicant.user?.country || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">ქალაქი:</span>
                  <p className="font-medium">{selectedApplicant.user?.city || 'N/A'}</p>
                </div>
                <div>
                  <span className="text-sm text-muted-foreground">მოთხოვნის თარიღი:</span>
                  <p className="font-medium">{selectedApplicant.created_at ? formatDate(selectedApplicant.created_at) : 'N/A'}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Contracts */}
          <div className="border rounded-lg p-4 sm:p-6 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-800">
            <h3 className="font-semibold mb-4 flex items-center gap-2 text-green-800 dark:text-green-200">
              <Building className="w-5 h-5" />
              სამუშაო გამოცდილება
              {selectedApplicant.user?.contracts && selectedApplicant.user.contracts.length > 0 && (
                <Badge variant="secondary" className="ml-2 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200">
                  {selectedApplicant.user.contracts.length}
                </Badge>
              )}
            </h3>

            {selectedApplicant.user?.contracts && selectedApplicant.user.contracts.length > 0 ? (
              <div className="space-y-3">
                {selectedApplicant.user.contracts.slice(0, 10).map((contract, index) => (
                  <div key={contract.id || index} className="border rounded-lg p-3 sm:p-4 bg-card shadow-sm">
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-4">
                      <div className="flex-1 min-w-0">
                        <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-3">
                          <h4 className="font-medium text-sm sm:text-base truncate">
                            {contract.company_name || 'უცნობი კომპანია'}
                          </h4>
                          <span className="text-xs sm:text-sm text-muted-foreground">
                            {contract.worker?.position || 'პოზიცია არ არის მითითებული'}
                          </span>
                        </div>

                        {contract.points && contract.points.length > 0 ? (
                          <div className="mt-2 flex flex-wrap gap-1">
                            {Object.entries(contract.points.reduce((acc, point) => {
                              const score = point.point || point.score || 0
                              acc[score] = (acc[score] || 0) + (point.count || 1)
                              return acc
                            }, {}))
                            .sort(([a], [b]) => parseInt(b) - parseInt(a))
                            .slice(0, 10) // Show all scores (up to 10)
                            .map(([score, count]) => (
                              <Badge
                                key={score}
                                variant="outline"
                                className={`text-xs ${
                                  parseInt(score) >= 8 ? 'border-green-500 text-green-700 bg-green-50' :
                                  parseInt(score) >= 6 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                                  'border-red-500 text-red-700 bg-red-50'
                                }`}
                              >
                                {score}★ ({count})
                              </Badge>
                            ))}
                          </div>
                        ) : (
                          <div className="mt-2">
                            <Badge variant="outline" className="text-xs text-muted-foreground">
                              ქულები არ არის
                            </Badge>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                {selectedApplicant.user.contracts.length > 10 && (
                  <div className="text-center text-sm text-muted-foreground">
                    და კიდევ {selectedApplicant.user.contracts.length - 10} კონტრაქტი...
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-6">
                <Building className="w-12 h-12 text-muted-foreground mx-auto mb-2" />
                <p className="text-muted-foreground">სამუშაო გამოცდილება არ არის</p>
              </div>
            )}
          </div>


        </div>
        </>
      )
    }

    // Embedded mode - render content directly without Dialog
    return (
      <>
        {/* Confirmation Alert for embedded list mode */}
        <ConfirmationAlert
          isOpen={showConfirmAlert}
          onClose={handleConfirmationCancel}
          onConfirm={handleConfirmationConfirm}
          title="აპლიკანტის დადასტურება"
          message={pendingConfirmation ?
            `ნამდვილად გსურთ ${pendingConfirmation.applicantName}-ის დადასტურება ამ ნოუდზე?\n\nკანდიდატის ხარისხი: ${pendingConfirmation.qualityText}\n\nგაფრთხილება: ეს მოქმედება შეუქცევადია და მხოლოდ ერთი აპლიკანტი შეიძლება იყოს არჩეული.`
            : ''
          }
          confirmText="დადასტურება"
          cancelText="გაუქმება"
          type={pendingConfirmation?.quality === 'excellent' ? 'success' :
                pendingConfirmation?.quality === 'good' ? 'default' :
                pendingConfirmation?.quality === 'average' ? 'warning' : 'danger'}
          isLoading={confirmingApplicant === pendingConfirmation?.applicantId}
        />

        <div className="flex flex-col h-full animate-in fade-in-0 slide-in-from-left-4 duration-300">
        <div className="p-4 border-b bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 border-border">
          <div className="text-center">
            <h3 className="text-lg font-semibold flex items-center justify-center gap-2 text-blue-800 dark:text-blue-200">
              <Users className="w-5 h-5" />
              აპლიკანტები ({totalApplicants})
            </h3>
            {totalApplicants > 0 && (
              <div className="flex flex-col items-center gap-1 mt-2">
                <p className="text-sm text-muted-foreground">
                  დალაგებულია ხარისხის მიხედვით (საუკეთესო პირველი)
                </p>
                <p className="text-xs text-muted-foreground">
                  {((currentPage - 1) * perPage) + 1}-{Math.min(currentPage * perPage, totalApplicants)} / {totalApplicants}
                </p>
              </div>
            )}
          </div>
        </div>

        <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="flex flex-col items-center justify-center py-16">
                <div className="w-16 h-16 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-800/30 dark:to-purple-800/30 rounded-full flex items-center justify-center mb-4 animate-pulse">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-500 dark:text-blue-400" />
                </div>
                <span className="text-lg font-medium text-gray-700 dark:text-gray-300">აპლიკანტების ჩატვირთვა...</span>
                <span className="text-sm text-muted-foreground mt-1">გთხოვთ დაელოდოთ</span>
              </div>
            ) : applicants.length === 0 ? (
              <div className="text-center py-16">
                <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-blue-100 to-purple-100 dark:from-blue-800/30 dark:to-purple-800/30 rounded-full flex items-center justify-center shadow-lg">
                  <Users className="h-10 w-10 text-blue-500 dark:text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold mb-3 text-gray-800 dark:text-gray-200">აპლიკანტები არ მოიძებნა</h3>
                <p className="text-muted-foreground text-base max-w-md mx-auto leading-relaxed">
                  ამ ნოუდისთვის ჯერ არ არის მოთხოვნები.
                  როდესაც ადამიანები დააფიქსირებენ მოთხოვნას, ისინი აქ გამოჩნდებიან.
                </p>
                <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800 max-w-sm mx-auto">
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    💡 ტიპი: აპლიკანტები ავტომატურად დალაგდებიან ხარისხის მიხედვით
                  </p>
                </div>
              </div>
            ) : (
              <div className="p-2 sm:p-4 space-y-3">
                {applicants.map((applicant, index) => (
                  <div
                    key={applicant.id}
                    className="border rounded-lg p-3 sm:p-4 bg-card hover:shadow-md transition-all duration-200 animate-in fade-in-0 slide-in-from-bottom-2"
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
                      {/* Left side - Basic info */}
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                          <User className="w-4 h-4 sm:w-5 sm:h-5 text-white" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-semibold text-foreground text-sm sm:text-base truncate">
                            {getApplicantDisplayName(applicant)}
                          </h3>
                          <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-xs sm:text-sm text-muted-foreground">
                            <span className="flex items-center gap-1">
                              <Star className="w-3 h-3" />
                              {applicant.user?.rate ? `${applicant.user.rate}/10` : 'N/A'}
                            </span>
                            <span>{applicant.user?.leave_count || 0} დატოვება</span>
                            {applicant.created_at && (
                              <span className="hidden sm:inline">{formatDate(applicant.created_at)}</span>
                            )}
                          </div>
                          {applicant.created_at && (
                            <div className="sm:hidden text-xs text-muted-foreground mt-1">
                              {formatDate(applicant.created_at)}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Right side - Actions */}
                      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-2 flex-shrink-0 w-full sm:w-auto">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleViewDetails(applicant)}
                          className="flex items-center justify-center gap-1 text-xs sm:text-sm px-2 sm:px-3"
                        >
                          <Eye className="w-3 h-3" />
                          <span className="hidden sm:inline">დეტალები</span>
                          <span className="sm:hidden">დეტალები</span>
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleConfirmApplicant(applicant.id, getApplicantDisplayName(applicant), applicant)}
                          disabled={confirmingApplicant === applicant.id}
                          className={`flex items-center justify-center gap-1 text-white text-xs sm:text-sm px-2 sm:px-3 ${
                            getApplicantQuality(applicant) === 'excellent' ? 'bg-green-600 hover:bg-green-700' :
                            getApplicantQuality(applicant) === 'good' ? 'bg-blue-600 hover:bg-blue-700' :
                            getApplicantQuality(applicant) === 'average' ? 'bg-yellow-600 hover:bg-yellow-700' :
                            'bg-red-600 hover:bg-red-700'
                          }`}
                        >
                          {confirmingApplicant === applicant.id ? (
                            <>
                              <Loader2 className="w-3 h-3 animate-spin" />
                              <span className="hidden sm:inline">დადასტურება...</span>
                              <span className="sm:hidden">დადასტურება...</span>
                            </>
                          ) : (
                            <>
                              <CheckCircle className="w-3 h-3" />
                              <span className="hidden sm:inline">დადასტურება</span>
                              <span className="sm:hidden">დადასტურება</span>
                            </>
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination */}
            {console.log('Pagination check:', { totalPages, currentPage, totalApplicants })}
            {totalPages > 1 && (
              <div className="p-3 sm:p-4 border-t bg-background/50 backdrop-blur-sm">
                <div className="flex flex-col sm:flex-row items-center justify-between gap-3">
                  {/* Page Info */}
                  <div className="text-xs sm:text-sm text-muted-foreground order-2 sm:order-1">
                    {((currentPage - 1) * perPage) + 1}-{Math.min(currentPage * perPage, totalApplicants)} / {totalApplicants}
                  </div>

                  {/* Page Controls */}
                  <div className="flex items-center gap-2 sm:gap-3 order-1 sm:order-2">
                    {/* Previous button */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="flex items-center gap-1 px-2 sm:px-3"
                    >
                      <ChevronLeft className="w-3 h-3 sm:w-4 sm:h-4" />
                      <span className="hidden sm:inline">წინა</span>
                    </Button>

                    {/* Page dots - only show on larger screens */}
                    <div className="hidden sm:flex items-center gap-1">
                      {Array.from({ length: Math.min(totalPages, 5) }, (_, index) => {
                        let pageNum
                        if (totalPages <= 5) {
                          pageNum = index + 1
                        } else {
                          // Smart pagination logic
                          if (currentPage <= 3) {
                            pageNum = index + 1
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + index
                          } else {
                            pageNum = currentPage - 2 + index
                          }
                        }

                        const isActive = pageNum === currentPage
                        return (
                          <button
                            key={pageNum}
                            onClick={() => handlePageChange(pageNum)}
                            className={`
                              w-3 h-3 rounded-full transition-all duration-300 ease-in-out cursor-pointer
                              ${isActive
                                ? 'bg-primary scale-125 shadow-lg ring-2 ring-primary/30'
                                : 'bg-muted-foreground/30 hover:bg-muted-foreground/60 hover:scale-110'
                              }
                            `}
                            aria-label={`გვერდი ${pageNum}`}
                          />
                        )
                      })}
                    </div>

                    {/* Page number display - mobile */}
                    <div className="text-xs sm:text-sm text-muted-foreground font-medium bg-muted/50 px-2 py-1 rounded-full">
                      {currentPage} / {totalPages}
                    </div>

                    {/* Next button */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="flex items-center gap-1 px-2 sm:px-3"
                    >
                      <span className="hidden sm:inline">შემდეგი</span>
                      <ChevronRight className="w-3 h-3 sm:w-4 sm:h-4" />
                    </Button>
                  </div>

                  {/* Page number display - desktop */}
                  <div className="hidden sm:block text-sm text-muted-foreground order-3">
                    გვერდი {currentPage} / {totalPages}
                  </div>
                </div>
              </div>
            )}
        </div>

        {/* Applicant Detail Modal for embedded mode */}
        {selectedApplicantState && (
          <Dialog open={showDetailModal} onOpenChange={setShowDetailModal}>
            <DialogContent className="sm:max-w-[900px] w-full max-w-[95vw] h-[700px] max-h-[90vh] overflow-hidden flex flex-col">
              <DialogHeader>
                <DialogTitle className="text-xl font-bold flex items-center gap-2">
                  <User className="w-5 h-5" />
                  აპლიკანტის დეტალური ინფორმაცია
                </DialogTitle>
              </DialogHeader>

              <div className="flex-1 overflow-y-auto p-4">
                <div className="space-y-6">
                  {/* User Basic Info */}
                  <div className="border rounded-lg p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                    <h3 className="font-semibold mb-4 flex items-center gap-2 text-blue-800">
                      <User className="w-5 h-5" />
                      ძირითადი ინფორმაცია
                    </h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-muted-foreground">სახელი:</span>
                        <p className="font-medium">
                          {getApplicantDisplayName(selectedApplicantState)}
                        </p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">რეიტინგი:</span>
                        <div className="flex items-center gap-2 mt-1">
                          <Star className="w-4 h-4 text-yellow-500" />
                          {selectedApplicantState.user?.rate ? (
                            <Badge
                              variant="outline"
                              className={`${
                                selectedApplicantState.user.rate >= 8 ? 'border-green-500 text-green-700 bg-green-50' :
                                selectedApplicantState.user.rate >= 6 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                                'border-red-500 text-red-700 bg-red-50'
                              }`}
                            >
                              {selectedApplicantState.user.rate}
                            </Badge>
                          ) : (
                            <span className="font-medium text-muted-foreground">N/A</span>
                          )}
                        </div>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">დატოვების რაოდენობა:</span>
                        <div className="mt-1">
                          <Badge
                            variant="outline"
                            className={`${
                              (selectedApplicantState.user?.leave_count || 0) === 0 ? 'border-green-500 text-green-700 bg-green-50' :
                              (selectedApplicantState.user?.leave_count || 0) <= 2 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                              'border-red-500 text-red-700 bg-red-50'
                            }`}
                          >
                            {selectedApplicantState.user?.leave_count || 0} ჯერ
                          </Badge>
                        </div>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">ქვეყანა:</span>
                        <p className="font-medium">{selectedApplicantState.user?.country || 'N/A'}</p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">ქალაქი:</span>
                        <p className="font-medium">{selectedApplicantState.user?.city || 'N/A'}</p>
                      </div>
                      <div>
                        <span className="text-sm text-muted-foreground">დაბადების თარიღი:</span>
                        <p className="font-medium">{selectedApplicantState.user?.birth_date ? formatDate(selectedApplicantState.user.birth_date) : 'N/A'}</p>
                      </div>
                    </div>
                  </div>

                  {/* Contracts */}
                  {selectedApplicantState.user?.contracts && selectedApplicantState.user.contracts.length > 0 && (
                    <div className="border rounded-lg p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                      <h3 className="font-semibold mb-4 flex items-center gap-2 text-green-800">
                        <Building className="w-5 h-5" />
                        კონტრაქტები
                        <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800">
                          {selectedApplicantState.user.contracts.length}
                        </Badge>
                      </h3>
                      <div className="space-y-4">
                        {selectedApplicantState.user.contracts.map((contract, index) => (
                          <div key={contract.id || index} className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-all duration-200">
                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-3">
                              <div>
                                <span className="text-sm text-muted-foreground">კომპანია:</span>
                                <p className="font-medium">{contract.company_name || 'N/A'}</p>
                              </div>
                              <div>
                                <span className="text-sm text-muted-foreground">პოზიცია:</span>
                                <p className="font-medium">{contract.worker?.position || 'N/A'}</p>
                              </div>
                            </div>

                            {contract.points && contract.points.length > 0 && (
                              <div>
                                <span className="text-sm text-muted-foreground">ტასკების შეფასება:</span>
                                <div className="mt-2 space-y-1">
                                  {Object.entries(contract.points.reduce((acc, point) => {
                                    const score = point.score || 0
                                    acc[score] = (acc[score] || 0) + 1
                                    return acc
                                  }, {}))
                                  .sort(([a], [b]) => parseInt(b) - parseInt(a))
                                  .map(([score, count]) => (
                                    <div key={score} className="flex items-center gap-2">
                                      <Badge
                                        variant="outline"
                                        className={`text-xs ${
                                          parseInt(score) >= 8 ? 'border-green-500 text-green-700 bg-green-50' :
                                          parseInt(score) >= 6 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                                          'border-red-500 text-red-700 bg-red-50'
                                        }`}
                                        title={`${score} ქულა - ${
                                          parseInt(score) >= 8 ? 'ძალიან კარგი' :
                                          parseInt(score) >= 6 ? 'კარგი' :
                                          'საჭიროებს გაუმჯობესებას'
                                        }`}
                                      >
                                        {score} ქულა
                                      </Badge>
                                      <span className="text-xs text-muted-foreground font-medium">
                                        {count} ჯერ
                                      </span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            )}

                            {contract.comment && (
                              <div className="mt-3">
                                <span className="text-sm text-muted-foreground">კომენტარი:</span>
                                <p className="text-sm mt-1">{contract.comment}</p>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </div>
      </>
    )
  }

  return (
    <>
      {/* Confirmation Alert */}
      <ConfirmationAlert
        isOpen={showConfirmAlert}
        onClose={handleConfirmationCancel}
        onConfirm={handleConfirmationConfirm}
        title="აპლიკანტის დადასტურება"
        message={pendingConfirmation ?
          `ნამდვილად გსურთ ${pendingConfirmation.applicantName}-ის დადასტურება ამ ნოუდზე?\n\nკანდიდატის ხარისხი: ${pendingConfirmation.qualityText}\n\nგაფრთხილება: ეს მოქმედება შეუქცევადია და მხოლოდ ერთი აპლიკანტი შეიძლება იყოს არჩეული.`
          : ''
        }
        confirmText="დადასტურება"
        cancelText="გაუქმება"
        type={pendingConfirmation?.quality === 'excellent' ? 'success' :
              pendingConfirmation?.quality === 'good' ? 'default' :
              pendingConfirmation?.quality === 'average' ? 'warning' : 'danger'}
        isLoading={confirmingApplicant === pendingConfirmation?.applicantId}
      />

      {/* Main Applicants List Modal */}
      <Dialog open={isOpen} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[800px] w-full max-w-[95vw] h-[600px] max-h-[85vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="text-xl font-bold text-center flex items-center justify-center gap-2">
              <Users className="w-5 h-5" />
              {nodeLabel} - აპლიკანტები ({totalApplicants})
            </DialogTitle>
            {applicants.length > 0 && (
              <p className="text-sm text-muted-foreground text-center">
                დალაგებულია ხარისხის მიხედვით (საუკეთესო პირველი)
              </p>
            )}
          </DialogHeader>

          <div className="flex-1 overflow-y-auto">
            {loading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-sm text-muted-foreground">იტვირთება...</p>
                </div>
              </div>
            ) : applicants.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">აპლიკანტები არ მოიძებნა</p>
                </div>
              </div>
            ) : (
              <div className="p-4 space-y-3">
                {applicants.map((applicant, index) => (
                  <div
                    key={applicant.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-all duration-200 cursor-pointer bg-white"
                    onClick={() => {
                      setSelectedApplicantState(applicant)
                      setShowDetailModal(true)
                    }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-100 text-blue-800 font-semibold text-sm">
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="font-medium">
                            {getApplicantDisplayName(applicant)}
                          </h4>
                          <div className="flex items-center gap-2 mt-1">
                            <Star className="w-4 h-4 text-yellow-500" />
                            {applicant.user?.rate ? (
                              <Badge
                                variant="outline"
                                className={`text-xs ${
                                  applicant.user.rate >= 8 ? 'border-green-500 text-green-700 bg-green-50' :
                                  applicant.user.rate >= 6 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                                  'border-red-500 text-red-700 bg-red-50'
                                }`}
                              >
                                {applicant.user.rate}
                              </Badge>
                            ) : (
                              <span className="text-xs text-muted-foreground">N/A</span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant="outline"
                          className={`text-xs ${
                            (applicant.user?.leave_count || 0) === 0 ? 'border-green-500 text-green-700 bg-green-50' :
                            (applicant.user?.leave_count || 0) <= 2 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                            'border-red-500 text-red-700 bg-red-50'
                          }`}
                        >
                          {applicant.user?.leave_count || 0} დატოვება
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Pagination for Modal */}
            {totalPages > 1 && (
              <div className="p-4 border-t bg-background/50 backdrop-blur-sm">
                <div className="flex items-center justify-between gap-3">
                  {/* Page Info */}
                  <div className="text-sm text-muted-foreground">
                    {((currentPage - 1) * perPage) + 1}-{Math.min(currentPage * perPage, totalApplicants)} / {totalApplicants}
                  </div>

                  {/* Page Controls */}
                  <div className="flex items-center gap-3">
                    {/* Previous button */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="flex items-center gap-1"
                    >
                      <ChevronLeft className="w-4 h-4" />
                      წინა
                    </Button>

                    {/* Page dots */}
                    <div className="flex items-center gap-1">
                      {Array.from({ length: Math.min(totalPages, 5) }, (_, index) => {
                        let pageNum
                        if (totalPages <= 5) {
                          pageNum = index + 1
                        } else {
                          // Smart pagination logic
                          if (currentPage <= 3) {
                            pageNum = index + 1
                          } else if (currentPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + index
                          } else {
                            pageNum = currentPage - 2 + index
                          }
                        }

                        const isActive = pageNum === currentPage
                        return (
                          <button
                            key={pageNum}
                            onClick={() => handlePageChange(pageNum)}
                            className={`
                              w-3 h-3 rounded-full transition-all duration-300 ease-in-out cursor-pointer
                              ${isActive
                                ? 'bg-primary scale-125 shadow-lg ring-2 ring-primary/30'
                                : 'bg-muted-foreground/30 hover:bg-muted-foreground/60 hover:scale-110'
                              }
                            `}
                            aria-label={`გვერდი ${pageNum}`}
                          />
                        )
                      })}
                    </div>

                    {/* Page number display */}
                    <div className="text-sm text-muted-foreground font-medium bg-muted/50 px-2 py-1 rounded-full">
                      {currentPage} / {totalPages}
                    </div>

                    {/* Next button */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                      className="flex items-center gap-1"
                    >
                      შემდეგი
                      <ChevronRight className="w-4 h-4" />
                    </Button>
                  </div>

                  {/* Page number display - desktop */}
                  <div className="text-sm text-muted-foreground">
                    გვერდი {currentPage} / {totalPages}
                  </div>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Applicant Detail Modal */}
      {selectedApplicantState && (
        <Dialog open={showDetailModal} onOpenChange={setShowDetailModal}>
          <DialogContent className="sm:max-w-[900px] w-full max-w-[95vw] h-[700px] max-h-[90vh] overflow-hidden flex flex-col">
            <DialogHeader>
              <div className="flex justify-between items-center">
                <DialogTitle className="text-xl font-bold flex items-center gap-2">
                  <User className="w-5 h-5" />
                  <span className="hidden sm:inline">აპლიკანტის დეტალური ინფორმაცია</span>
                  <span className="sm:hidden">დეტალები</span>
                </DialogTitle>
                <Button
                  onClick={() => handleConfirmApplicant(selectedApplicantState.id, getApplicantDisplayName(selectedApplicantState), selectedApplicantState)}
                  disabled={confirmingApplicant === selectedApplicantState.id}
                  className={`flex items-center gap-2 px-3 py-2 text-white font-semibold text-sm ${
                    getApplicantQuality(selectedApplicantState) === 'excellent' ? 'bg-green-600 hover:bg-green-700' :
                    getApplicantQuality(selectedApplicantState) === 'good' ? 'bg-blue-600 hover:bg-blue-700' :
                    getApplicantQuality(selectedApplicantState) === 'average' ? 'bg-yellow-600 hover:bg-yellow-700' :
                    'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  {confirmingApplicant === selectedApplicantState.id ? (
                    <>
                      <Loader2 className="w-4 h-4 animate-spin" />
                      <span className="hidden sm:inline">დადასტურება...</span>
                      <span className="sm:hidden">...</span>
                    </>
                  ) : (
                    <>
                      <CheckCircle className="w-4 h-4" />
                      <span className="hidden lg:inline">
                        {getApplicantQuality(selectedApplicantState) === 'excellent' ? '🌟 შესანიშნავი კანდიდატი!' :
                         getApplicantQuality(selectedApplicantState) === 'good' ? '👍 კარგი არჩევანი' :
                         getApplicantQuality(selectedApplicantState) === 'average' ? '📊 დადასტურება' :
                         '⚠️ ფრთხილად - დადასტურება'}
                      </span>
                      <span className="lg:hidden">
                        {getApplicantQuality(selectedApplicantState) === 'excellent' ? '🌟 შესანიშნავი!' :
                         getApplicantQuality(selectedApplicantState) === 'good' ? '👍 კარგი' :
                         getApplicantQuality(selectedApplicantState) === 'average' ? '📊 დადასტურება' :
                         '⚠️ ფრთხილად'}
                      </span>
                    </>
                  )}
                </Button>
              </div>
            </DialogHeader>

            <div className="flex-1 overflow-y-auto p-4">
              <div className="space-y-6">
                {/* User Basic Info */}
                <div className="border rounded-lg p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
                  <h3 className="font-semibold mb-4 flex items-center gap-2 text-blue-800">
                    <User className="w-5 h-5" />
                    ძირითადი ინფორმაცია
                  </h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm text-muted-foreground">სახელი:</span>
                      <p className="font-medium">
                        {getApplicantDisplayName(selectedApplicantState)}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">რეიტინგი:</span>
                      <div className="flex items-center gap-2 mt-1">
                        <Star className="w-4 h-4 text-yellow-500" />
                        {selectedApplicantState.user?.rate ? (
                          <Badge
                            variant="outline"
                            className={`${
                              selectedApplicantState.user.rate >= 8 ? 'border-green-500 text-green-700 bg-green-50' :
                              selectedApplicantState.user.rate >= 6 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                              'border-red-500 text-red-700 bg-red-50'
                            }`}
                          >
                            {selectedApplicantState.user.rate}
                          </Badge>
                        ) : (
                          <span className="font-medium text-muted-foreground">N/A</span>
                        )}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">დატოვების რაოდენობა:</span>
                      <div className="mt-1">
                        <Badge
                          variant="outline"
                          className={`${
                            (selectedApplicantState.user?.leave_count || 0) === 0 ? 'border-green-500 text-green-700 bg-green-50' :
                            (selectedApplicantState.user?.leave_count || 0) <= 2 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                            'border-red-500 text-red-700 bg-red-50'
                          }`}
                        >
                          {selectedApplicantState.user?.leave_count || 0} ჯერ
                        </Badge>
                      </div>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">ქვეყანა:</span>
                      <p className="font-medium">{selectedApplicantState.user?.country || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">ქალაქი:</span>
                      <p className="font-medium">{selectedApplicantState.user?.city || 'N/A'}</p>
                    </div>
                    <div>
                      <span className="text-sm text-muted-foreground">დაბადების თარიღი:</span>
                      <p className="font-medium">{selectedApplicantState.user?.birth_date ? formatDate(selectedApplicantState.user.birth_date) : 'N/A'}</p>
                    </div>
                  </div>
                </div>

                {/* Contracts */}
                {selectedApplicantState.user?.contracts && selectedApplicantState.user.contracts.length > 0 && (
                  <div className="border rounded-lg p-6 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200">
                    <h3 className="font-semibold mb-4 flex items-center gap-2 text-green-800">
                      <Building className="w-5 h-5" />
                      კონტრაქტები
                      <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800">
                        {selectedApplicantState.user.contracts.length}
                      </Badge>
                    </h3>
                    <div className="space-y-4">
                      {selectedApplicantState.user.contracts.map((contract, index) => (
                        <div key={contract.id || index} className="border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-all duration-200">
                          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-3">
                            <div>
                              <span className="text-sm text-muted-foreground">კომპანია:</span>
                              <p className="font-medium">{contract.company_name || 'N/A'}</p>
                            </div>
                            <div>
                              <span className="text-sm text-muted-foreground">პოზიცია:</span>
                              <p className="font-medium">{contract.worker?.position || 'N/A'}</p>
                            </div>
                          </div>
                          
                          {contract.points && contract.points.length > 0 && (
                            <div>
                              <span className="text-sm text-muted-foreground">ტასკების შეფასება:</span>
                              <div className="mt-2 space-y-1">
                                {Object.entries(contract.points.reduce((acc, point) => {
                                  const score = point.score || 0
                                  acc[score] = (acc[score] || 0) + 1
                                  return acc
                                }, {}))
                                .sort(([a], [b]) => parseInt(b) - parseInt(a))
                                .map(([score, count]) => (
                                  <div key={score} className="flex items-center gap-2">
                                    <Badge
                                      variant="outline"
                                      className={`text-xs ${
                                        parseInt(score) >= 8 ? 'border-green-500 text-green-700 bg-green-50' :
                                        parseInt(score) >= 6 ? 'border-yellow-500 text-yellow-700 bg-yellow-50' :
                                        'border-red-500 text-red-700 bg-red-50'
                                      }`}
                                      title={`${score} ქულა - ${
                                        parseInt(score) >= 8 ? 'ძალიან კარგი' :
                                        parseInt(score) >= 6 ? 'კარგი' :
                                        'საჭიროებს გაუმჯობესებას'
                                      }`}
                                    >
                                      {score} ქულა
                                    </Badge>
                                    <span className="text-xs text-muted-foreground font-medium">
                                      {count} ჯერ
                                    </span>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                          
                          {contract.comment && (
                            <div className="mt-3">
                              <span className="text-sm text-muted-foreground">კომენტარი:</span>
                              <p className="text-sm mt-1">{contract.comment}</p>
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </>
  )
}
