# ApplicantNotification WebSocket Implementation

## 📋 მიმოხილვა

ამ იმპლემენტაციით დავამატეთ რეალურ დროში ნოტიფიკაციების მხარდაჭერა, როდესაც ახალი აპლიკანტი აგზავნის მოთხოვნას ვაკანტურ ნოუდზე.

## 🔧 განხორციელებული ცვლილებები

### 1. notifications-manager.jsx
- **დამატებული ახალი channel**: `laravel-database-new.applicant.for.user-${user.id}`
- **ApplicantNotification ივენთის მოსმენა**: `.ApplicantNotification` და `ApplicantNotification`
- **ნოტიფიკაციის ფორმატირება**: აპლიკანტის მონაცემების შენახვა `applicant_data` ობიექტში
- **Network data განახლება**: `networkDataUpdated` ივენთის გაშვება აპლიკანტების რაოდენობის განახლებისთვის

### 2. notification-modal.jsx
- **ApplicantNotification-ის მხარდაჭერა**: სპეციალური UI აპლიკანტის ინფორმაციისთვის
- **აპლიკანტის დეტალები**: პოზიცია, რეიტინგი, გასვლები, უარყოფები
- **განახლებული ტიტული**: "🔔 ახალი აპლიკანტი!" ApplicantNotification-ისთვის
- **WebSocket ნოტიფიკაციების ხელმისაწვდომობა**: `applicant-` პრეფიქსის მხარდაჭერა

## 📡 WebSocket Channel და Event

### Channel
```
laravel-database-new.applicant.for.user-{user_id}
```

### Event Name
```
ApplicantNotification
```

### Event Data Structure
```javascript
{
  node_position: "Frontend Developer",    // ნოუდის პოზიცია
  user_id: 123,                          // აპლიკანტის ID
  user_rate: 8,                          // აპლიკანტის რეიტინგი (1-10)
  user_leave_count: 2,                   // გასვლების რაოდენობა
  user_reject_count: 1                   // უარყოფების რაოდენობა
}
```

## 🎯 ფუნქციონალი

### რეალურ დროში ნოტიფიკაცია
- როდესაც ახალი აპლიკანტი აგზავნის მოთხოვნას, მყისიერად ჩნდება ნოტიფიკაცია
- ნოტიფიკაცია შეიცავს აპლიკანტის სრულ ინფორმაციას
- ავტომატურად განახლდება აპლიკანტების რაოდენობა UI-ში

### ნოტიფიკაციის შინაარსი
- **ტიტული**: "🔔 ახალი აპლიკანტი!"
- **ტექსტი**: "🔔 ახალი აპლიკანტი! {პოზიცია} მოთხოვნა გამოიგზავნა"
- **დეტალები**:
  - პოზიცია
  - რეიტინგი (ვარსკვლავებით)
  - გასვლების რაოდენობა
  - უარყოფების რაოდენობა

## 🧪 ტესტირება

### ტესტის ფაილი
`test-applicant-notification.html` - WebSocket კავშირის ტესტირებისთვის

### ტესტის ნაბიჯები
1. გახსენით `http://localhost:3001` (მთავარი აპლიკაცია)
2. გახსენით `test-applicant-notification.html` (ტესტის ფაილი)
3. შეიყვანეთ User ID
4. დააჭირეთ "Connect WebSocket"
5. დააჭირეთ "Send Test Notification"

### Laravel Backend-ის მხრიდან
Laravel-ში უნდა გაიშვას ივენთი:
```php
broadcast(new ApplicantNotification($contract))->toOthers();
```

## 🔄 ინტეგრაცია არსებულ სისტემასთან

### ApplicantsButton
- ავტომატურად განახლდება აპლიკანტების რაოდენობა
- `networkDataUpdated` ივენთს უსმენს

### NotificationsManager
- ყველა WebSocket ნოტიფიკაცია ჩნდება modal-ში
- ნოტიფიკაციები ინახება store-ში

### Store Integration
- ნოტიფიკაციები ემატება `notifications` array-ში
- `unreadCount` ავტომატურად განახლდება

## 🚀 გამოყენება

### Frontend-ზე
ნოტიფიკაციები ავტომატურად მუშაობს, როდესაც:
1. იუზერი ავტორიზებულია
2. WebSocket კავშირი დამყარებულია
3. Laravel backend აგზავნის ApplicantNotification ივენთს

### Backend-ზე (Laravel)
```php
// Event class-ში
public function broadcastOn(): array
{
    return [
        new Channel('laravel-database-new.applicant.for.user-' . $this->user->id)
    ];
}

public function broadcastAs(): string
{
    return 'ApplicantNotification';
}

public function broadcastWith(): array
{
    return [
        'node_position' => $this->contract->worker->position ?? null,
        'user_id' => $this->user->id ?? null,
        'user_rate' => $this->user->rate ?? null,
        'user_leave_count' => $this->user->leave_count ?? null,
        'user_reject_count' => $this->user->reject_count ?? null,
    ];
}
```

## ✅ შედეგი

ახლა იუზერები რეალურ დროში მიიღებენ ნოტიფიკაციებს, როდესაც ვინმე აპლიკაციას გაგზავნის მათ ვაკანტურ ნოუდზე, სრული ინფორმაციით აპლიკანტის შესახებ.
