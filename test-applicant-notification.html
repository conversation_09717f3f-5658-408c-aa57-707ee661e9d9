<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test ApplicantNotification WebSocket</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/2.5.0/socket.io.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/laravel-echo/1.15.3/echo.iife.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔔 ApplicantNotification WebSocket ტესტი</h1>
        
        <div class="form-group">
            <label for="userId">User ID:</label>
            <input type="number" id="userId" value="2" placeholder="Enter user ID">
        </div>

        <div class="form-group">
            <label for="nodePosition">Node Position:</label>
            <input type="text" id="nodePosition" value="Frontend Developer" placeholder="Enter position">
        </div>

        <div class="form-group">
            <label for="userRate">User Rate (1-10):</label>
            <input type="number" id="userRate" value="8" min="1" max="10" placeholder="Enter rating">
        </div>

        <div class="form-group">
            <label for="userLeaveCount">Leave Count:</label>
            <input type="number" id="userLeaveCount" value="2" min="0" placeholder="Enter leave count">
        </div>

        <div class="form-group">
            <label for="userRejectCount">Reject Count:</label>
            <input type="number" id="userRejectCount" value="1" min="0" placeholder="Enter reject count">
        </div>

        <button onclick="connectWebSocket()">🔌 Connect WebSocket</button>
        <button onclick="sendTestNotification()">📤 Send Test Notification</button>
        <button onclick="disconnectWebSocket()">❌ Disconnect</button>
        <button onclick="clearLog()">🗑️ Clear Log</button>

        <div id="status" class="status info">
            📡 Ready to connect...
        </div>

        <div id="log" class="log">
            <div>📋 Log messages will appear here...</div>
        </div>
    </div>

    <script>
        let echo = null;
        let isConnected = false;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#6c757d';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }

        function connectWebSocket() {
            const userId = document.getElementById('userId').value;
            
            if (!userId) {
                updateStatus('❌ Please enter a User ID', 'error');
                return;
            }

            try {
                // Initialize Echo
                echo = new window.Echo({
                    broadcaster: 'socket.io',
                    host: 'http://127.0.0.1:6001',
                    forceNew: true,
                    transports: ['websocket', 'polling']
                });

                log('🔌 Initializing WebSocket connection...', 'info');

                // Listen to the applicant notification channel
                const channelName = `laravel-database-new.applicant.for.user-${userId}`;
                const applicantChannel = echo.channel(channelName);
                
                log(`📡 Listening to channel: ${channelName}`, 'info');

                // Listen for ApplicantNotification events
                applicantChannel
                    .listen('.ApplicantNotification', (data) => {
                        log('🔔 ApplicantNotification received (with dot):', 'success');
                        log(JSON.stringify(data, null, 2), 'success');
                    })
                    .listen('ApplicantNotification', (data) => {
                        log('🔔 ApplicantNotification received (without dot):', 'success');
                        log(JSON.stringify(data, null, 2), 'success');
                    });

                isConnected = true;
                updateStatus('✅ WebSocket connected successfully!', 'success');
                log('✅ WebSocket connection established', 'success');

            } catch (error) {
                log(`❌ WebSocket connection failed: ${error.message}`, 'error');
                updateStatus('❌ Connection failed', 'error');
            }
        }

        function sendTestNotification() {
            if (!isConnected) {
                updateStatus('❌ Please connect WebSocket first', 'error');
                return;
            }

            const userId = document.getElementById('userId').value;
            const nodePosition = document.getElementById('nodePosition').value;
            const userRate = document.getElementById('userRate').value;
            const userLeaveCount = document.getElementById('userLeaveCount').value;
            const userRejectCount = document.getElementById('userRejectCount').value;

            const testData = {
                node_position: nodePosition,
                user_id: parseInt(userId),
                user_rate: parseInt(userRate),
                user_leave_count: parseInt(userLeaveCount),
                user_reject_count: parseInt(userRejectCount)
            };

            log('📤 Sending test notification with data:', 'info');
            log(JSON.stringify(testData, null, 2), 'info');

            // Simulate receiving the notification (since we can't actually send from client)
            // In real scenario, this would come from Laravel backend
            setTimeout(() => {
                log('🔔 Simulating ApplicantNotification event...', 'info');
                
                // Trigger the event handlers manually for testing
                if (echo) {
                    log('📨 Test notification would be sent to Laravel backend', 'success');
                    updateStatus('📨 Test notification simulated (check Laravel logs)', 'success');
                }
            }, 1000);
        }

        function disconnectWebSocket() {
            if (echo) {
                echo.disconnect();
                echo = null;
                isConnected = false;
                updateStatus('❌ WebSocket disconnected', 'info');
                log('❌ WebSocket disconnected', 'info');
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>📋 Log cleared...</div>';
        }

        // Auto-connect on page load
        window.addEventListener('load', () => {
            log('🚀 Page loaded, ready for testing', 'info');
        });
    </script>
</body>
</html>
