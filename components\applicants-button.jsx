"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Users } from "lucide-react"
import { api } from "@/lib/api"
import { AllApplicantsModal } from "./all-applicants-modal"
import useStore from "@/lib/store"

export function ApplicantsButton({ nodes: propNodes = [] }) {
  // Use nodes from store if available, otherwise use props
  const { nodes: storeNodes } = useStore()
  const nodes = storeNodes.length > 0 ? storeNodes : propNodes
  const [newApplicantsCount, setNewApplicantsCount] = useState(0)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(false)

  // Calculate applicants count from network data (contracts_count)
  const calculateApplicantsCount = () => {
    try {
      let totalCount = 0

      // Sum up contracts_count from all user's owned nodes
      nodes.forEach(node => {
        if (node.data && node.data.is_mine) {
          const contractsCount = node.data.contracts_count || 0
          totalCount += contractsCount
        }
      })

      console.log('Calculated total applicants count:', totalCount)
      setNewApplicantsCount(totalCount)
    } catch (error) {
      console.error('Error calculating applicants count:', error)
      setNewApplicantsCount(0)
    }
  }

  // Calculate count when nodes change
  useEffect(() => {
    calculateApplicantsCount()
  }, [nodes])

  // Listen for network updates to refresh count
  useEffect(() => {
    const handleNetworkUpdate = () => {
      calculateApplicantsCount()
    }

    window.addEventListener('networkDataUpdated', handleNetworkUpdate)
    return () => {
      window.removeEventListener('networkDataUpdated', handleNetworkUpdate)
    }
  }, [nodes])

  // Check if user has any owned nodes
  const userHasNodes = nodes.some(node => node.data && node.data.is_mine)

  // Only render button if user has nodes
  if (!userHasNodes) {
    return null
  }

  return (
    <>
      <Button
        onClick={() => setIsModalOpen(true)}
        variant="outline"
        size="sm"
        className="shadow-lg relative"
        title="ყველა აპლიკანტი"
        disabled={loading}
      >
        <Users className="h-4 w-4 mr-2" />
        <span className="hidden sm:inline">აპლიკანტები</span>
        <span className="sm:hidden">აპლ.</span>

        {/* Red badge for new applicants */}
        {newApplicantsCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 font-medium shadow-lg">
            {newApplicantsCount > 99 ? '99+' : newApplicantsCount}
          </span>
        )}
      </Button>

      <AllApplicantsModal
        isOpen={isModalOpen}
        onOpenChange={setIsModalOpen}
        onRefreshCount={calculateApplicantsCount}
      />
    </>
  )
}
