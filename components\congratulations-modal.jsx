"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Trophy, 
  Star, 
  Sparkles, 
  Crown, 
  Award, 
  PartyPopper,
  CheckCircle,
  Users,
  Calendar,
  MapPin,
  X
} from "lucide-react"
import { toast } from "sonner"

export function CongratulationsModal({ isOpen, onOpenChange, jobData, onClose }) {
  const [showConfetti, setShowConfetti] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  useEffect(() => {
    if (isOpen) {
      setShowConfetti(true)
      setCurrentStep(0)
      
      // Auto-advance through steps
      const timer = setTimeout(() => {
        setCurrentStep(1)
      }, 1000)

      return () => clearTimeout(timer)
    }
  }, [isOpen])

  const handleClose = () => {
    setShow<PERSON>onfetti(false)
    setCurrentStep(0)
    onOpenChange(false)
    
    // Trigger dashboard refresh
    window.dispatchEvent(new CustomEvent('networkDataUpdated'))
    
    if (onClose) {
      onClose()
    }
  }

  const getPositionIcon = (position) => {
    const iconMap = {
      'manager': Crown,
      'lead': Award,
      'senior': Star,
      'developer': Trophy,
      'analyst': CheckCircle,
      'coordinator': Users
    }
    
    const IconComponent = iconMap[position?.toLowerCase()] || Trophy
    return <IconComponent className="w-8 h-8" />
  }

  const getPositionColor = (position) => {
    const colorMap = {
      'manager': 'bg-purple-500',
      'lead': 'bg-blue-500',
      'senior': 'bg-green-500',
      'developer': 'bg-orange-500',
      'analyst': 'bg-cyan-500',
      'coordinator': 'bg-pink-500'
    }
    
    return colorMap[position?.toLowerCase()] || 'bg-yellow-500'
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] w-[95vw] max-h-[90vh] overflow-hidden border-0 bg-gradient-to-br from-yellow-50 via-orange-50 to-red-50 dark:from-yellow-950 dark:via-orange-950 dark:to-red-950">
        {/* Close button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="absolute right-4 top-4 z-50 rounded-full w-8 h-8 p-0 hover:bg-white/20"
        >
          <X className="w-4 h-4" />
        </Button>

        {/* Confetti Animation */}
        <AnimatePresence>
          {showConfetti && (
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {[...Array(20)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-3 h-3 bg-yellow-400 rounded-full"
                  initial={{
                    x: Math.random() * 600,
                    y: -20,
                    rotate: 0,
                    scale: 0
                  }}
                  animate={{
                    y: 700,
                    rotate: 360,
                    scale: [0, 1, 0.5, 0]
                  }}
                  transition={{
                    duration: 3,
                    delay: Math.random() * 2,
                    ease: "easeOut"
                  }}
                />
              ))}
            </div>
          )}
        </AnimatePresence>

        <div className="relative z-10 p-6">
          <DialogHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 0.8, type: "spring", bounce: 0.5 }}
              className="mx-auto"
            >
              <div className={`w-20 h-20 rounded-full ${getPositionColor(jobData?.position)} flex items-center justify-center text-white shadow-2xl`}>
                {getPositionIcon(jobData?.position)}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <DialogTitle className="text-3xl font-bold bg-gradient-to-r from-yellow-600 via-orange-600 to-red-600 bg-clip-text text-transparent">
                🎉 გილოცავთ! 🎉
              </DialogTitle>
            </motion.div>
          </DialogHeader>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6 }}
            className="mt-8 space-y-6"
          >
            {/* Main congratulations message */}
            <div className="text-center space-y-4">
              <div className="flex items-center justify-center gap-2">
                <Sparkles className="w-6 h-6 text-yellow-500" />
                <h2 className="text-2xl font-semibold text-gray-800 dark:text-gray-200">
                  თქვენ წარმატებით დაიკავეთ ადგილი
                </h2>
                <Sparkles className="w-6 h-6 text-yellow-500" />
              </div>
              
              <p className="text-lg text-gray-600 dark:text-gray-300">
                თქვენი კანდიდატურა დადასტურდა და ახლა ოფიციალურად ხართ ნაწილი ჩვენი გუნდისა
              </p>
            </div>

            {/* Action button */}
            <div className="flex justify-center pt-4">
              <Button
                onClick={handleClose}
                className="bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600 text-white font-semibold px-8 py-3 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105"
              >
                <PartyPopper className="w-5 h-5 mr-2" />
                შესანიშნავი!
              </Button>
            </div>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
