<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React Flow Test</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/reactflow@11/dist/umd/index.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/reactflow@11/dist/style.css">
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        #root {
            width: 100vw;
            height: 100vh;
        }
        .react-flow__edge-path {
            stroke: #3b82f6;
            stroke-width: 2;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script>
        const { useState, useCallback } = React;
        const { ReactFlow, Background, Controls, useNodesState, useEdgesState } = ReactFlowLib;

        const initialNodes = [
            {
                id: 'root',
                type: 'default',
                position: { x: 400, y: 200 },
                data: { label: 'Root Node' }
            },
            {
                id: 'child1',
                type: 'default',
                position: { x: 200, y: 350 },
                data: { label: 'Child 1' }
            },
            {
                id: 'child2',
                type: 'default',
                position: { x: 600, y: 350 },
                data: { label: 'Child 2' }
            }
        ];

        const initialEdges = [
            {
                id: 'edge-root-child1',
                source: 'root',
                target: 'child1',
                type: 'smoothstep',
                style: { stroke: '#3b82f6', strokeWidth: 2 }
            },
            {
                id: 'edge-root-child2',
                source: 'root',
                target: 'child2',
                type: 'smoothstep',
                style: { stroke: '#3b82f6', strokeWidth: 2 }
            }
        ];

        function App() {
            const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes);
            const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges);

            const onConnect = useCallback((params) => {
                console.log('Connection attempt:', params);
            }, []);

            return React.createElement(ReactFlow, {
                nodes: nodes,
                edges: edges,
                onNodesChange: onNodesChange,
                onEdgesChange: onEdgesChange,
                onConnect: onConnect,
                nodesDraggable: true,
                nodesConnectable: false,
                fitView: true,
                style: { width: '100%', height: '100%' }
            }, [
                React.createElement(Background, { key: 'bg' }),
                React.createElement(Controls, { key: 'controls' })
            ]);
        }

        ReactDOM.render(React.createElement(App), document.getElementById('root'));
    </script>
</body>
</html>
