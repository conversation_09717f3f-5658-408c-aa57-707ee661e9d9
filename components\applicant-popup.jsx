"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { X, User, <PERSON> } from "lucide-react"

export function ApplicantPopup({ applicantData, isVisible, onClose }) {
  const [timeLeft, setTimeLeft] = useState(4)

  useEffect(() => {
    if (!isVisible) return

    // Reset timer when popup becomes visible
    setTimeLeft(4)

    const timer = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          // Use setTimeout to avoid state update during render
          setTimeout(() => onClose(), 0)
          return 0
        }
        return prev - 1
      })
    }, 1000)

    return () => clearInterval(timer)
  }, [isVisible, onClose])

  if (!applicantData) return null

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, x: 300, scale: 0.8 }}
          animate={{ opacity: 1, x: 0, scale: 1 }}
          exit={{ opacity: 0, x: 300, scale: 0.8 }}
          transition={{ 
            type: "spring", 
            stiffness: 300, 
            damping: 30,
            duration: 0.3 
          }}
          className="fixed top-4 right-4 z-[9999] w-80 bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700 overflow-hidden"
        >
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-500 px-4 py-3 text-white relative">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <User className="w-5 h-5" />
                <span className="font-semibold">ახალი აპლიკანტი!</span>
              </div>
              <button
                onClick={onClose}
                className="text-white/80 hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
            
            {/* Progress bar */}
            <motion.div
              className="absolute bottom-0 left-0 h-1 bg-white/30"
              initial={{ width: "100%" }}
              animate={{ width: "0%" }}
              transition={{ duration: 4, ease: "linear" }}
            />
          </div>

          {/* Content */}
          <div className="p-4 space-y-3">
            {/* Position */}
            {applicantData.node_position && (
              <div className="text-center">
                <p className="text-lg font-medium text-gray-800 dark:text-gray-200">
                  📋 {applicantData.node_position}
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  პოზიციაზე მოთხოვნა გამოიგზავნა
                </p>
              </div>
            )}

            {/* Quick stats */}
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="bg-yellow-50 dark:bg-yellow-900/30 rounded p-2 text-center">
                {applicantData.user_rate ? (
                  <div className="flex items-center justify-center gap-1 text-yellow-600 dark:text-yellow-400">
                    <Star className="w-3 h-3 fill-current" />
                    <span className="font-bold">{applicantData.user_rate}/10</span>
                  </div>
                ) : (
                  <div className="flex items-center justify-center gap-1 text-blue-600 dark:text-blue-400">
                    <Star className="w-3 h-3 stroke-current stroke-2 fill-none" />
                    <span className="font-bold text-xs">NEW</span>
                  </div>
                )}
                <div className="text-yellow-700 dark:text-yellow-300 mt-1">რეიტინგი</div>
              </div>
              
              {applicantData.user_leave_count !== undefined && (
                <div className="bg-orange-50 dark:bg-orange-900/30 rounded p-2 text-center">
                  <div className="font-bold text-orange-600 dark:text-orange-400">
                    {applicantData.user_leave_count}
                  </div>
                  <div className="text-orange-700 dark:text-orange-300 mt-1">გასვლა</div>
                </div>
              )}
              
              {applicantData.user_reject_count !== undefined && (
                <div className="bg-red-50 dark:bg-red-900/30 rounded p-2 text-center">
                  <div className="font-bold text-red-600 dark:text-red-400">
                    {applicantData.user_reject_count}
                  </div>
                  <div className="text-red-700 dark:text-red-300 mt-1">უარყოფა</div>
                </div>
              )}
            </div>

            {/* Timer */}
            <div className="text-center">
              <span className="text-xs text-gray-500 dark:text-gray-400">
                დაიხურება {timeLeft} წამში
              </span>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
