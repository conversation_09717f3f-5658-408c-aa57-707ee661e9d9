"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { AlertTriangle, Loader2, X } from "lucide-react"

export function DeleteNodeModal({ 
  isOpen, 
  onOpenChange, 
  nodeLabel, 
  onConfirm, 
  isLoading = false 
}) {
  const handleConfirm = () => {
    onConfirm()
  }

  const handleCancel = () => {
    onOpenChange(false)
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md mx-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-red-600 dark:text-red-400">
            <AlertTriangle className="w-5 h-5" />
            ნოუდის წაშლა
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Warning message */}
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <AlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0 mt-0.5" />
              <div className="space-y-2">
                <h3 className="font-semibold text-red-800 dark:text-red-200">
                  მკაცრი გაფრთხილება!
                </h3>
                <p className="text-sm text-red-700 dark:text-red-300 leading-relaxed">
                  თუ წაშლით ნოუდს <strong>"{nodeLabel}"</strong>, მაშინ:
                </p>
                <ul className="text-sm text-red-700 dark:text-red-300 space-y-1 ml-4">
                  <li>• ყველა მისი შვილი ნოუდი გაქრება</li>
                  <li>• ყველა შვილიშვილი (შთამომავლობა) გაქრება</li>
                  <li>• მთელი ტოტი სამუდამოდ წაიშლება</li>
                  <li>• ყველა ამ დეპარტამენტზე დაქვემდებარებული თანამშრომელი დაკარგავს სამუშაო პოზიციას</li>
                </ul>
                <p className="text-sm font-semibold text-red-800 dark:text-red-200 mt-3">
                  ეს მოქმედება შეუქცევადია!
                </p>
              </div>
            </div>
          </div>

          {/* Confirmation text */}
          <div className="text-center">
            <p className="text-sm text-muted-foreground">
              ნამდვილად გსურთ ნოუდის და მისი მთელი შთამომავლობის წაშლა?
            </p>
          </div>

          {/* Action buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1"
              disabled={isLoading}
            >
              გაუქმება
            </Button>
            <Button
              variant="destructive"
              onClick={handleConfirm}
              className="flex-1"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  იშლება...
                </>
              ) : (
                <>
                  <X className="w-4 h-4 mr-2" />
                  წაშლა
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
