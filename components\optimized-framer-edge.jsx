"use client";

import React, { useRef, useEffect, useState, useMemo } from 'react';
import { motion, useMotionValue, useTransform, useAnimationFrame } from 'framer-motion';
import useStore from '@/lib/store';

export const OptimizedFramerEdge = React.memo(function OptimizedFramerEdge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  isActive = false,
  duration = 2000,
  className = "",
  strokeWidth = 2,
  stroke = "#10b981"
}) {
  const { edgeAnimationsEnabled } = useStore();
  const pathRef = useRef(null);
  const progress = useMotionValue(0);
  const [pathLength, setPathLength] = useState(0);
  const lastUpdateTime = useRef(0);

  // Calculate path data
  const deltaY = targetY - sourceY;
  const controlPointOffset = Math.abs(deltaY) * 0.5;

  // Memoize path data to avoid recalculation
  const pathData = useMemo(() => 
    `M ${sourceX} ${sourceY} C ${sourceX} ${sourceY + controlPointOffset} ${targetX} ${targetY - controlPointOffset} ${targetX} ${targetY}`,
    [sourceX, sourceY, targetX, targetY, controlPointOffset]
  );

  // Get path length when path changes
  useEffect(() => {
    if (pathRef.current) {
      const length = pathRef.current.getTotalLength();
      setPathLength(length);
    }
  }, [pathData]);

  // Optimized animation frame with throttling
  useAnimationFrame((time) => {
    // Throttle to 30 FPS for better performance
    if (time - lastUpdateTime.current < 33.33) {
      return;
    }
    
    if (isActive && pathLength > 0 && edgeAnimationsEnabled) {
      const pxPerMillisecond = pathLength / duration;
      const currentProgress = (time * pxPerMillisecond) % pathLength;
      progress.set(currentProgress);
      lastUpdateTime.current = time;
    } else if (!isActive || !edgeAnimationsEnabled) {
      progress.set(0);
    }
  });

  // Optimized transforms with memoization
  const x = useTransform(progress, (val) => {
    if (pathRef.current && pathLength > 0) {
      const point = pathRef.current.getPointAtLength(val);
      return point.x;
    }
    return sourceX;
  });

  const y = useTransform(progress, (val) => {
    if (pathRef.current && pathLength > 0) {
      const point = pathRef.current.getPointAtLength(val);
      return point.y;
    }
    return sourceY;
  });

  return (
    <g className={className}>
      {/* Base edge path */}
      <path
        ref={pathRef}
        d={pathData}
        fill="none"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
        style={{
          borderRadius: '8px',
        }}
        className="transition-none"
      />

      {/* Moving light effect - only render when active */}
      {isActive && edgeAnimationsEnabled && pathLength > 0 && (
        <>
          {/* Main moving light */}
          <motion.circle
            cx={x}
            cy={y}
            r="6"
            fill={stroke}
            className="drop-shadow-lg"
            style={{
              filter: `drop-shadow(0 0 12px ${stroke})`,
            }}
          />

          {/* Outer glow - simplified */}
          <motion.circle
            cx={x}
            cy={y}
            r="12"
            fill="none"
            stroke={stroke}
            strokeWidth="2"
            opacity="0.4"
          />

          {/* Inner bright core */}
          <motion.circle
            cx={x}
            cy={y}
            r="3"
            fill="#ffffff"
            opacity="0.8"
          />
        </>
      )}
    </g>
  );
});
