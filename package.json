{"name": "front", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@uiw/react-md-editor": "^4.0.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.12", "laravel-echo": "^2.1.7", "lucide-react": "^0.525.0", "next": "15.4.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "react": "19.1.0", "react-dom": "19.1.0", "reactflow": "^11.11.4", "socket.io-client": "^4.8.1", "sonner": "^2.0.6", "sweetalert2": "^11.22.2", "tailwind-merge": "^3.3.1", "zustand": "^5.0.6"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4", "tw-animate-css": "^1.3.5"}}