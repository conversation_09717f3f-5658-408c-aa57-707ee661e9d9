"use client";

import React, { useRef, useEffect, useCallback, useMemo } from 'react';
import useStore from '@/lib/store';

// Ultra-optimized Canvas-based moving edge component
export const OptimizedMovingEdge = React.memo(function OptimizedMovingEdge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  isActive = false,
  duration = 2000,
  className = "",
  strokeWidth = 2,
  stroke = "#10b981"
}) {
  const { edgeAnimationsEnabled } = useStore();
  const canvasRef = useRef(null);
  const animationRef = useRef(null);
  const lastFrameTime = useRef(0);

  // Calculate path data
  const deltaX = targetX - sourceX;
  const deltaY = targetY - sourceY;
  const controlPointOffset = Math.abs(deltaY) * 0.5;

  // Memoize expensive calculations
  const animationData = useMemo(() => {
    const padding = 30; // Reduced padding for better performance
    const minX = Math.min(sourceX, targetX) - padding;
    const maxX = Math.max(sourceX, targetX) + padding;
    const minY = Math.min(sourceY, targetY) - padding;
    const maxY = Math.max(sourceY, targetY) + padding;
    
    const width = maxX - minX;
    const height = maxY - minY;
    const localSourceX = sourceX - minX;
    const localSourceY = sourceY - minY;
    const localTargetX = targetX - minX;
    const localTargetY = targetY - minY;
    const approximateLength = Math.sqrt(deltaX * deltaX + deltaY * deltaY) * 1.1;
    
    return {
      minX, minY, width, height,
      localSourceX, localSourceY, localTargetX, localTargetY,
      approximateLength,
      speed: approximateLength / duration
    };
  }, [sourceX, sourceY, targetX, targetY, deltaX, deltaY, duration]);

  // Optimized Bezier curve calculation
  const getBezierPoint = useCallback((t) => {
    const oneMinusT = 1 - t;
    const tSquared = t * t;
    const oneMinusTSquared = oneMinusT * oneMinusT;
    
    return {
      x: oneMinusTSquared * animationData.localSourceX + 
         2 * oneMinusT * t * animationData.localSourceX + 
         tSquared * animationData.localTargetX,
      y: oneMinusTSquared * animationData.localSourceY + 
         2 * oneMinusT * t * (animationData.localSourceY + controlPointOffset) + 
         tSquared * animationData.localTargetY
    };
  }, [animationData.localSourceX, animationData.localSourceY, animationData.localTargetX, animationData.localTargetY, controlPointOffset]);

  // Ultra-optimized animation loop
  useEffect(() => {
    if (!canvasRef.current || !isActive || !edgeAnimationsEnabled) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d', {
      alpha: true,
      desynchronized: true,
      willReadFrequently: false
    });

    // Set canvas size with minimal DPI scaling
    const dpr = Math.min(window.devicePixelRatio || 1, 2);
    canvas.width = animationData.width * dpr;
    canvas.height = animationData.height * dpr;
    canvas.style.width = `${animationData.width}px`;
    canvas.style.height = `${animationData.height}px`;
    ctx.scale(dpr, dpr);

    // Pre-calculate styles for better performance
    const strokeColor = stroke;
    const glowColor = stroke + '40';
    const coreColor = 'rgba(255, 255, 255, 0.8)';

    let particleOffset = 0;
    let isPageVisible = !document.hidden;
    let isInViewport = true;

    // Visibility handlers
    const handleVisibilityChange = () => {
      isPageVisible = !document.hidden;
    };

    const observer = new IntersectionObserver((entries) => {
      isInViewport = entries[0].isIntersecting;
    }, { threshold: 0.1 });

    observer.observe(canvas);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Ultra-optimized animation function
    const animate = (currentTime) => {
      // Throttle to 30 FPS for optimal performance
      if (currentTime - lastFrameTime.current < 33.33) {
        animationRef.current = requestAnimationFrame(animate);
        return;
      }

      if (!isActive || !edgeAnimationsEnabled || !isPageVisible || !isInViewport) {
        ctx.clearRect(0, 0, animationData.width, animationData.height);
        return;
      }

      const deltaTime = currentTime - lastFrameTime.current;
      lastFrameTime.current = currentTime;

      // Update particle position
      particleOffset += animationData.speed * deltaTime;
      if (particleOffset > animationData.approximateLength) {
        particleOffset = 0;
      }

      const t = particleOffset / animationData.approximateLength;
      const point = getBezierPoint(t);
      const scale = 0.8 + Math.sin(t * Math.PI * 2) * 0.1;

      // Clear and draw in one pass
      ctx.clearRect(0, 0, animationData.width, animationData.height);

      // Draw base path
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = strokeWidth;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';

      ctx.beginPath();
      ctx.moveTo(animationData.localSourceX, animationData.localSourceY);
      ctx.bezierCurveTo(
        animationData.localSourceX, animationData.localSourceY + controlPointOffset,
        animationData.localTargetX, animationData.localTargetY - controlPointOffset,
        animationData.localTargetX, animationData.localTargetY
      );
      ctx.stroke();

      // Draw particle with minimal operations
      // Outer glow
      ctx.fillStyle = glowColor;
      ctx.beginPath();
      ctx.arc(point.x, point.y, 12 * scale, 0, Math.PI * 2);
      ctx.fill();

      // Main particle
      ctx.fillStyle = strokeColor;
      ctx.beginPath();
      ctx.arc(point.x, point.y, 6 * scale, 0, Math.PI * 2);
      ctx.fill();

      // Inner core
      ctx.fillStyle = coreColor;
      ctx.beginPath();
      ctx.arc(point.x, point.y, 3 * scale, 0, Math.PI * 2);
      ctx.fill();

      animationRef.current = requestAnimationFrame(animate);
    };

    lastFrameTime.current = performance.now();
    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      observer.disconnect();
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      ctx.clearRect(0, 0, animationData.width, animationData.height);
    };
  }, [isActive, edgeAnimationsEnabled, animationData, stroke, strokeWidth, controlPointOffset, getBezierPoint]);

  return (
    <>
      {/* Base SVG path for fallback */}
      <g className={className}>
        <path
          d={`M ${sourceX} ${sourceY} C ${sourceX} ${sourceY + controlPointOffset} ${targetX} ${targetY - controlPointOffset} ${targetX} ${targetY}`}
          fill="none"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{
            borderRadius: '8px',
          }}
          className="transition-none"
        />
      </g>
      
      {/* Canvas for GPU-accelerated animation */}
      {isActive && edgeAnimationsEnabled && (
        <div 
          style={{ 
            position: 'fixed', 
            left: `${animationData.minX}px`,
            top: `${animationData.minY}px`,
            pointerEvents: 'none', 
            zIndex: 1000
          }}
        >
          <canvas 
            ref={canvasRef}
            style={{
              display: 'block'
            }}
          />
        </div>
      )}
    </>
  );
});
