"use client"

import { useAuth } from "@/lib/auth-context"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { ThemeToggle } from "@/components/theme-toggle"
import { NotificationsButton } from "@/components/notifications-button"
import { NotificationsManager } from "@/components/notifications-manager"
import { Brain, LogOut } from "lucide-react"
import { toast } from "sonner"

export function DashboardLayout({ children }) {
  const { user, logout } = useAuth()

  const handleLogout = async () => {
    try {
      await logout()
      toast.success("Logged out successfully")
      window.location.href = "/"
    } catch (error) {
      toast.error("Failed to log out")
    }
  }

  const getUserInitials = (name) => {
    if (!name) return "U"
    return name
      .split(" ")
      .map(word => word[0])
      .join("")
      .toUpperCase()
      .slice(0, 2)
  }



  return (
    <>
      <div className="min-h-screen bg-background">
        {/* Header */}
        <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 relative z-50">
          <div className="flex h-14 sm:h-16 items-center px-4 sm:px-6">
            <div className="flex items-center space-x-2">
              <Brain className="h-5 w-5 sm:h-6 sm:w-6 text-primary" />
              <span className="text-lg sm:text-xl font-bold">FlowMind</span>
            </div>

            <div className="ml-auto flex items-center space-x-2 sm:space-x-4">
              <NotificationsButton />
              <ThemeToggle />

              <div className="flex items-center space-x-2 sm:space-x-3">
                <Avatar className="h-7 w-7 sm:h-8 sm:w-8">
                  <AvatarImage src={user?.image} />
                  <AvatarFallback className="text-xs sm:text-sm">
                    {getUserInitials(user?.name)}
                  </AvatarFallback>
                </Avatar>

                <div className="hidden md:block">
                  <p className="text-sm font-medium">{user?.name}</p>
                  <p className="text-xs text-muted-foreground">{user?.email}</p>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleLogout}
                  className="flex items-center space-x-1 sm:space-x-2"
                >
                  <LogOut className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="hidden sm:inline">Logout</span>
                </Button>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content */}
        <main className="h-[calc(100vh-3.5rem)] sm:h-[calc(100vh-4rem)] relative z-0">
          {children}
        </main>
      </div>

      {/* Notifications Manager - Outside main layout to avoid z-index issues */}
      <NotificationsManager />
    </>
  )
}
