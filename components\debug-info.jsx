"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-context"
import { tokenManager } from "@/lib/api"
import { Button } from "@/components/ui/button"

export function DebugInfo() {
  const { user, loading, isAuthenticated } = useAuth()
  const [tokenInfo, setTokenInfo] = useState(null)

  useEffect(() => {
    setTokenInfo({
      hasToken: tokenManager.isAuthenticated(),
      token: tokenManager.getToken()?.substring(0, 20) + '...' || 'No token'
    })
  }, [user])

  return (
    <div className="fixed bottom-4 right-4 bg-black/80 text-white p-4 rounded-lg text-xs max-w-sm">
      <h4 className="font-bold mb-2">Debug Info</h4>
      <div className="space-y-1">
        <div>Loading: {loading ? 'Yes' : 'No'}</div>
        <div>Authenticated: {isAuthenticated ? 'Yes' : 'No'}</div>
        <div>User: {user ? JSON.stringify(user, null, 2) : 'None'}</div>
        <div>Token: {tokenInfo?.hasToken ? 'Present' : 'None'}</div>
        <div>Token Preview: {tokenInfo?.token}</div>
      </div>
      <Button 
        size="sm" 
        variant="outline" 
        className="mt-2 text-xs h-6"
        onClick={() => console.log('Auth State:', { user, loading, isAuthenticated, tokenInfo })}
      >
        Log to Console
      </Button>
    </div>
  )
}
