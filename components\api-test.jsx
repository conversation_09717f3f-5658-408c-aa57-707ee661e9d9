"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { api, ApiError } from "@/lib/api"
import { toast } from "sonner"

export function ApiTest() {
  const [testResults, setTestResults] = useState([])
  const [isLoading, setIsLoading] = useState(false)

  const addResult = (test, success, message, data = null) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    }])
  }

  const testRegistration = async () => {
    setIsLoading(true)
    try {
      const testUser = {
        name: 'Test User Frontend',
        email: `test${Date.now()}@example.com`,
        password: 'password123',
        birth_date: '1990-01-01',
        gender: 'male'
      }

      const response = await api.auth.register(testUser)
      
      if (response.success) {
        addResult('Registration', true, 'User registered successfully', response.data)
        toast.success('Registration test passed!')
        return testUser
      } else {
        addResult('Registration', false, response.message || 'Registration failed')
        toast.error('Registration test failed')
        return null
      }
    } catch (error) {
      const message = error instanceof ApiError ? error.message : error.message
      addResult('Registration', false, `Error: ${message}`)
      toast.error('Registration test error')
      return null
    } finally {
      setIsLoading(false)
    }
  }

  const testLogin = async (email, password) => {
    setIsLoading(true)
    try {
      const response = await api.auth.login({ email, password })
      
      if (response.success) {
        addResult('Login', true, 'Login successful', response.data)
        toast.success('Login test passed!')
        return response.data.token
      } else {
        addResult('Login', false, response.message || 'Login failed')
        toast.error('Login test failed')
        return null
      }
    } catch (error) {
      const message = error instanceof ApiError ? error.message : error.message
      addResult('Login', false, `Error: ${message}`)
      toast.error('Login test error')
      return null
    } finally {
      setIsLoading(false)
    }
  }

  const testNetwork = async () => {
    setIsLoading(true)
    try {
      const response = await api.network.getNetwork()
      addResult('Network', true, 'Network data retrieved', response)
      toast.success('Network test passed!')
      return response
    } catch (error) {
      const message = error instanceof ApiError ? error.message : error.message
      addResult('Network', false, `Error: ${message}`)
      toast.error('Network test failed')
      return null
    } finally {
      setIsLoading(false)
    }
  }

  const runFullTest = async () => {
    setTestResults([])
    
    // Test registration
    const testUser = await testRegistration()
    if (!testUser) return
    
    // Test login
    const token = await testLogin(testUser.email, testUser.password)
    if (!token) return
    
    // Test network
    await testNetwork()
    
    toast.success('All API tests completed!')
  }

  const clearResults = () => {
    setTestResults([])
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">API Integration Test</h2>
      
      <div className="flex gap-4 mb-6">
        <Button onClick={runFullTest} disabled={isLoading}>
          {isLoading ? 'Testing...' : 'Run Full Test'}
        </Button>
        <Button onClick={testRegistration} disabled={isLoading} variant="outline">
          Test Registration
        </Button>
        <Button onClick={() => testLogin('<EMAIL>', 'password123')} disabled={isLoading} variant="outline">
          Test Login
        </Button>
        <Button onClick={testNetwork} disabled={isLoading} variant="outline">
          Test Network
        </Button>
        <Button onClick={clearResults} variant="outline">
          Clear Results
        </Button>
      </div>

      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Test Results:</h3>
        {testResults.length === 0 ? (
          <p className="text-muted-foreground">No tests run yet</p>
        ) : (
          <div className="space-y-2">
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`p-3 rounded border ${
                  result.success 
                    ? 'bg-green-50 border-green-200 text-green-800' 
                    : 'bg-red-50 border-red-200 text-red-800'
                }`}
              >
                <div className="flex justify-between items-start">
                  <div>
                    <span className="font-medium">{result.test}</span>
                    <span className={`ml-2 px-2 py-1 rounded text-xs ${
                      result.success ? 'bg-green-200' : 'bg-red-200'
                    }`}>
                      {result.success ? 'PASS' : 'FAIL'}
                    </span>
                  </div>
                  <span className="text-xs text-muted-foreground">{result.timestamp}</span>
                </div>
                <p className="mt-1">{result.message}</p>
                {result.data && (
                  <details className="mt-2">
                    <summary className="cursor-pointer text-sm">View Data</summary>
                    <pre className="mt-1 text-xs bg-white p-2 rounded border overflow-auto">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
