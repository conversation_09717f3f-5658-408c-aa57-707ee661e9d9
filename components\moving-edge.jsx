"use client";

import React, { useRef, useEffect, useState } from 'react';
import { motion, useMotionValue, useTransform, useAnimationFrame } from 'framer-motion';
import { SmoothStepEdge, StraightEdge } from 'reactflow';
import useStore from '@/lib/store';
import { OptimizedFramerEdge } from './optimized-framer-edge';

export const MovingEdge = React.memo(function MovingEdge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  isActive = false,
  duration = 2000,
  className = "",
  strokeWidth = 2,
  stroke = "#10b981" // Default to green color
}) {
  const { edgeAnimationsEnabled } = useStore();

  // Use optimized Framer Motion version when animations are enabled
  if (edgeAnimationsEnabled) {
    return (
      <OptimizedFramerEdge
        sourceX={sourceX}
        sourceY={sourceY}
        targetX={targetX}
        targetY={targetY}
        isActive={true} // Always active when animations are enabled
        duration={duration}
        className={className}
        strokeWidth={strokeWidth}
        stroke={stroke}
      />
    );
  }

  // Fallback to simple static path when animations are disabled
  const deltaY = targetY - sourceY;
  const controlPointOffset = Math.abs(deltaY) * 0.5;
  const pathData = `M ${sourceX} ${sourceY} C ${sourceX} ${sourceY + controlPointOffset} ${targetX} ${targetY - controlPointOffset} ${targetX} ${targetY}`;

  return (
    <g className={className}>
      {/* Base edge path */}
      <path
        d={pathData}
        fill="none"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
        style={{
          borderRadius: '8px',
        }}
        className="transition-none"
      />
    </g>
  );
});

// Custom edge component for React Flow
export const CustomEdge = React.memo(function CustomEdge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  data,
  style,
  // Filter out React Flow specific props that shouldn't be passed to DOM
  animated,
  labelStyle,
  labelShowBg,
  labelBgStyle,
  labelBgPadding,
  label,
  labelBgBorderRadius,
  interactionWidth,
  ...props
}) {
  const { edgeAnimationsEnabled } = useStore();

  return (
    <MovingEdge
      sourceX={sourceX}
      sourceY={sourceY}
      targetX={targetX}
      targetY={targetY}
      isActive={(data?.isActive || false) && edgeAnimationsEnabled}
      stroke={style?.stroke}
      strokeWidth={style?.strokeWidth}
      // Only pass safe props
      className={props.className}
    />
  );
});

// Wrapper that always uses beautiful MovingEdge component (like during pulse)
export const SafeSmoothStepEdge = React.memo(function SafeSmoothStepEdge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  style,
  // Filter out React Flow specific props that shouldn't be passed to DOM
  animated,
  labelStyle,
  labelShowBg,
  labelBgStyle,
  labelBgPadding,
  labelBgBorderRadius,
  interactionWidth,
  ...props
}) {
  const { edgeAnimationsEnabled } = useStore();

  // Use MovingEdge with animation based on user preference
  return (
    <MovingEdge
      sourceX={sourceX}
      sourceY={sourceY}
      targetX={targetX}
      targetY={targetY}
      isActive={edgeAnimationsEnabled} // Respect user preference
      stroke={style?.stroke || '#10b981'}
      strokeWidth={style?.strokeWidth || 2}
      className={props.className}
    />
  );
});

// Wrapper that always uses beautiful MovingEdge component (like during pulse)
export const SafeStraightEdge = React.memo(function SafeStraightEdge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  style,
  // Filter out React Flow specific props that shouldn't be passed to DOM
  animated,
  labelStyle,
  labelShowBg,
  labelBgStyle,
  labelBgPadding,
  labelBgBorderRadius,
  interactionWidth,
  ...props
}) {
  const { edgeAnimationsEnabled } = useStore();

  // Use MovingEdge with animation based on user preference
  return (
    <MovingEdge
      sourceX={sourceX}
      sourceY={sourceY}
      targetX={targetX}
      targetY={targetY}
      isActive={edgeAnimationsEnabled} // Respect user preference
      stroke={style?.stroke || '#10b981'}
      strokeWidth={style?.strokeWidth || 2}
      className={props.className}
    />
  );
});
