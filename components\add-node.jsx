"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>si<PERSON>, useReactFlow } from "reactflow"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, DialogContent, DialogHeader, <PERSON>alogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Plus, Loader2 } from "lucide-react"
import useStore from "@/lib/store"
import { toast } from "sonner"

export default function AddNode({ id, data }) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [newNodePosition, setNewNodePosition] = useState("")
  const [newNodeReward, setNewNodeReward] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isRepositioning, setIsRepositioning] = useState(false)
  const { addNodeToBackend } = useStore()
  const { getNodes, setNodes } = useReactFlow()

  // ფუნქცია რომ შეამოწმოს არის თუ არა პოზიცია დაკავებული
  const isPositionOccupied = (testPos, excludeId = null) => {
    const nodes = getNodes()
    const minDistance = 180 // მინიმალური დისტანცია ნოუდებს შორის (გაზრდილი)

    return nodes.some(node => {
      if (node.id === excludeId) return false
      if (node.type === 'addNode') return false // + ნოუდები არ ითვლება კონფლიქტად

      const distance = Math.sqrt(
        Math.pow(node.position.x - testPos.x, 2) +
        Math.pow(node.position.y - testPos.y, 2)
      )
      return distance < minDistance
    })
  }

  // ფუნქცია რომ იპოვოს თავისუფალი პოზიცია
  const findFreePosition = (currentPos) => {
    const step = 180 // ნაბიჯის ზომა
    const maxAttempts = 24 // მაქსიმალური მცდელობები

    // ჯერ შევამოწმოთ მიმდინარე პოზიცია
    if (!isPositionOccupied(currentPos, id)) {
      return currentPos
    }

    console.log(`+ ნოუდი ${id} ეფარება სხვა ნოუდს, ვეძებთ თავისუფალ ადგილს...`)

    // თუ მიმდინარე პოზიცია დაკავებულია, ვეძებთ თავისუფალს
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      const radius = step * Math.ceil(attempt / 8) // რადიუსი იზრდება
      const angle = (attempt * 45) % 360 // 45 გრადუსით ვტრიალებთ

      const newPos = {
        x: currentPos.x + radius * Math.cos(angle * Math.PI / 180),
        y: currentPos.y + radius * Math.sin(angle * Math.PI / 180)
      }

      if (!isPositionOccupied(newPos, id)) {
        console.log(`+ ნოუდისთვის ${id} ნაპოვნია თავისუფალი ადგილი:`, newPos)
        return newPos
      }
    }

    // თუ ვერ ვიპოვეთ, დავაბრუნოთ მიმდინარე პოზიცია + რანდომ ოფსეტი
    const fallbackPos = {
      x: currentPos.x + Math.random() * 300 - 150,
      y: currentPos.y + Math.random() * 300 - 150
    }
    console.log(`+ ნოუდისთვის ${id} გამოყენებულია fallback პოზიცია:`, fallbackPos)
    return fallbackPos
  }

  // ფუნქცია რომ შეამოწმოს და გადაიტანოს + ნოუდი თუ საჭიროა
  const checkAndRepositionNode = () => {
    const nodes = getNodes()
    const currentNode = nodes.find(node => node.id === id)

    if (!currentNode) return

    const freePosition = findFreePosition(currentNode.position)

    // თუ პოზიცია შეიცვალა, განვაახლოთ
    if (Math.abs(freePosition.x - currentNode.position.x) > 5 ||
        Math.abs(freePosition.y - currentNode.position.y) > 5) {

      setIsRepositioning(true)

      const updatedNodes = nodes.map(node =>
        node.id === id
          ? { ...node, position: freePosition, zIndex: 1000 } // ყოველთვის ზედა ფენაზე
          : node
      )
      setNodes(updatedNodes)

      // შევინახოთ ახალი პოზიცია localStorage-ში
      const savedPositions = JSON.parse(localStorage.getItem('flowmind-node-positions') || '{}')
      savedPositions[id] = freePosition
      localStorage.setItem('flowmind-node-positions', JSON.stringify(savedPositions))

      console.log(`+ ნოუდი ${id} გადატანილია პოზიციაზე:`, freePosition)

      // ვიზუალური ინდიკატორის გამორთვა 1 წამის შემდეგ
      setTimeout(() => {
        setIsRepositioning(false)
      }, 1000)
    }
  }

  // შევამოწმოთ პოზიცია კომპონენტის mount-ზე
  useEffect(() => {
    const timer = setTimeout(() => {
      checkAndRepositionNode()

      // ასევე დავრწმუნდეთ რომ + ნოუდი ყოველთვის ზედა ფენაზეა
      const nodes = getNodes()
      const currentNode = nodes.find(node => node.id === id)
      if (currentNode && currentNode.zIndex !== 1000) {
        const updatedNodes = nodes.map(node =>
          node.id === id
            ? { ...node, zIndex: 1000 }
            : node
        )
        setNodes(updatedNodes)
      }
    }, 500) // მცირე დაყოვნება რომ ყველა ნოუდი ჩაიტვირთოს

    return () => clearTimeout(timer)
  }, [])

  // შევამოწმოთ პოზიცია ყოველ 3 წამში
  useEffect(() => {
    const interval = setInterval(() => {
      checkAndRepositionNode()

      // ასევე დავრწმუნდეთ რომ + ნოუდი ყოველთვის ზედა ფენაზეა
      const nodes = getNodes()
      const currentNode = nodes.find(node => node.id === id)
      if (currentNode && currentNode.zIndex !== 1000) {
        const updatedNodes = nodes.map(node =>
          node.id === id
            ? { ...node, zIndex: 1000 }
            : node
        )
        setNodes(updatedNodes)
        console.log(`+ ნოუდი ${id} z-index განახლდა`)
      }
    }, 3000)

    return () => clearInterval(interval)
  }, [])

  // მოვუსმინოთ ნეთვორკის განახლებას
  useEffect(() => {
    const handleNetworkUpdate = () => {
      console.log('Network updated, checking + node position...')
      setTimeout(() => {
        checkAndRepositionNode()

        // ასევე დავრწმუნდეთ რომ + ნოუდი ყოველთვის ზედა ფენაზეა
        const nodes = getNodes()
        const currentNode = nodes.find(node => node.id === id)
        if (currentNode && currentNode.zIndex !== 1000) {
          const updatedNodes = nodes.map(node =>
            node.id === id
              ? { ...node, zIndex: 1000 }
              : node
          )
          setNodes(updatedNodes)
        }
      }, 1000) // ოდნავ მეტი დაყოვნება ნეთვორკის განახლების შემდეგ
    }

    window.addEventListener('networkDataUpdated', handleNetworkUpdate)
    return () => window.removeEventListener('networkDataUpdated', handleNetworkUpdate)
  }, [])

  const handleAddNode = async () => {
    if (!newNodePosition.trim() || !newNodeReward.trim()) {
      toast.error("გთხოვთ შეავსოთ ყველა ველი")
      return
    }

    const reward = parseFloat(newNodeReward)
    if (isNaN(reward) || reward <= 0) {
      toast.error("რიუორდი უნდა იყოს დადებითი რიცხვი")
      return
    }

    // Check if parent already has 10 children
    const nodes = getNodes()
    const parentNode = nodes.find(node => node.id === data.parentId)
    if (parentNode && parentNode.data && parentNode.data.children) {
      const childrenCount = parentNode.data.children.filter(child => child && !child.isAddNode).length
      if (childrenCount >= 10) {
        toast.error("ნოუდს უკვე აქვს მაქსიმალური რაოდენობის (10) შვილი")
        return
      }
    }

    setIsLoading(true)
    try {
      await addNodeToBackend(data.parentId, newNodePosition.trim(), reward)
      toast.success("ნოუდი წარმატებით დაემატა!")
      setNewNodePosition("")
      setNewNodeReward("")
      setIsModalOpen(false)

      // ახალი ნოუდის დამატების შემდეგ შევამოწმოთ + ნოუდის პოზიცია
      setTimeout(() => {
        checkAndRepositionNode()

        // ასევე დავრწმუნდეთ რომ + ნოუდი ყოველთვის ზედა ფენაზეა
        const nodes = getNodes()
        const currentNode = nodes.find(node => node.id === id)
        if (currentNode && currentNode.zIndex !== 1000) {
          const updatedNodes = nodes.map(node =>
            node.id === id
              ? { ...node, zIndex: 1000 }
              : node
          )
          setNodes(updatedNodes)
        }
      }, 1500) // 1.5 წამის შემდეგ შევამოწმოთ

    } catch (error) {
      console.error('Failed to add node:', error)
      toast.error(error.message || "ნოუდის დამატება ვერ მოხერხდა")
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancel = () => {
    setNewNodePosition("")
    setNewNodeReward("")
    setIsModalOpen(false)
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleAddNode()
    } else if (e.key === "Escape") {
      handleCancel()
    }
  }

  return (
    <div className="relative">
      {/* Input Handle (top) */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-primary border-2 border-background"
      />

      {/* Add Node Button */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogTrigger asChild>
          <div
            className={`group border-2 border-l-4 rounded-lg shadow-lg w-[220px] h-[120px] transition-all duration-200 hover:shadow-xl hover:scale-105 relative border-border cursor-pointer bg-card border-dashed border-primary/50 z-[1000] hover:border-yellow-500 hover:bg-yellow-50 dark:hover:bg-yellow-900/20 ${
              isRepositioning ? 'animate-pulse border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20' : ''
            }`}
            style={{ zIndex: 1000 }}
          >
            <div className="p-3 h-full flex flex-col justify-center">
              <div className="text-center">
                <div className="mb-1">
                  <div className={`text-5xl font-light transition-colors duration-200 ${
                    isRepositioning ? 'text-yellow-600' : 'text-primary/70 group-hover:text-yellow-600'
                  }`}>+</div>
                </div>
                <div className="text-xs text-muted-foreground group-hover:text-yellow-700 dark:group-hover:text-yellow-400 transition-colors duration-200 font-medium">
                  მოითხოვე დამხმარე
                </div>
                {isRepositioning && (
                  <div className="text-[10px] text-yellow-600 font-medium mt-1">
                    პოზიციის კორექტირება...
                  </div>
                )}
              </div>
            </div>
          </div>
        </DialogTrigger>
        
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-center">ახალი ნოუდის დამატება</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">პოზიციის სახელი</label>
              <Input
                value={newNodePosition}
                onChange={(e) => setNewNodePosition(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="მაგ: Frontend Developer"
                className="w-full"
              />
            </div>
            
            <div className="space-y-2">
              <label className="text-sm font-medium">რიუორდი (%)</label>
              <Input
                value={newNodeReward}
                onChange={(e) => setNewNodeReward(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="მაგ: 5"
                type="number"
                min="0"
                step="0.1"
                className="w-full"
              />
            </div>
            
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={handleCancel} disabled={isLoading}>
                გაუქმება
              </Button>
              <Button
                onClick={handleAddNode}
                disabled={!newNodePosition.trim() || !newNodeReward.trim() || isLoading}
              >
                {isLoading ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : null}
                დამატება
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
