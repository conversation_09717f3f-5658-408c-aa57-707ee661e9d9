"use client"

import { use<PERSON><PERSON><PERSON>, useEffect, use<PERSON>em<PERSON>, useState } from "react"
import { useAuth } from "@/lib/auth-context"
import { api } from "@/lib/api"
import ReactFlow, {
  Background,
  Controls,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  ConnectionLineType,
  Panel,
  useReactFlow,
} from "reactflow"
import "reactflow/dist/style.css"
import { CustomNode } from "@/components/custom-node"
import AddNode from "@/components/add-node"
import { Button } from "@/components/ui/button"
import { RotateCcw, Target, User } from "lucide-react"
import useStore from "@/lib/store"
import { toast } from "sonner"
import { CustomEdge, SafeSmoothStepEdge, SafeStraightEdge } from "@/components/moving-edge"
import { StatusIndicator } from "@/components/status-indicator"
import { AnimationToggle } from "@/components/animation-toggle"
import { ApplicantsButton } from "@/components/applicants-button"
import { NodeTasksModal } from "@/components/tasks-modal"

const defaultEdgeOptions = {
  type: "smoothstep", // Always use smoothstep for beautiful curved edges
  style: {
    stroke: "#10b981", // Green color - beautiful and consistent
    strokeWidth: 2,
    strokeLinecap: "round",
    strokeLinejoin: "round",
    borderRadius: "8px", // Rounded corners like pulse button
  },
}

// Component that uses useReactFlow hook inside ReactFlow
function MyNodeControls({ nodes, onOpenModal }) {
  const { fitView } = useReactFlow()

  const handleFocusMyNode = useCallback(() => {
    const myNode = nodes.find(node => node.data && node.data.is_mine)
    if (myNode) {
      fitView({
        nodes: [myNode],
        duration: 800,
        padding: 0.3,
      })
      toast.success("ჩემს ნოუდზე გადავედი")
    } else {
      toast.error("ჩემი ნოუდი ვერ მოიძებნა")
    }
  }, [nodes, fitView])

  // Check if user has any owned nodes
  const userHasNodes = nodes.some(node => node.data && node.data.is_mine)

  // Only render buttons if user has nodes
  if (!userHasNodes) {
    return null
  }

  return (
    <>
      <Button
        onClick={handleFocusMyNode}
        variant="outline"
        size="sm"
        className="shadow-lg"
        title="ჩემს ნოუდზე გადასვლა"
      >
        <Target className="h-4 w-4 mr-2" />
        ჩემი ნოუდი
      </Button>
      <Button
        onClick={onOpenModal}
        variant="outline"
        size="sm"
        className="shadow-lg"
        title="ჩემი ნოუდის გახსნა"
      >
        <User className="h-4 w-4 mr-2" />
        ნოუდი
      </Button>
    </>
  )
}

export function DiagramCanvas() {
  const { user } = useAuth()
  const {
    nodes: storeNodes,
    edges: storeEdges,
    saveUserDiagram,
    edgeAnimationsEnabled,
  } = useStore()

  const [nodes, setNodes, onNodesChange] = useNodesState([])
  const [edges, setEdges, onEdgesChange] = useEdgesState([])
  const [isLoading, setIsLoading] = useState(true)
  const [originalPositions, setOriginalPositions] = useState({})
  const [myNodeModalOpen, setMyNodeModalOpen] = useState(false)

  // Function to load saved positions from localStorage
  const loadSavedPositions = useCallback((nodes) => {
    const savedPositions = JSON.parse(localStorage.getItem('flowmind-node-positions') || '{}')
    console.log('Loading saved positions:', savedPositions)
    console.log('Nodes to apply positions to:', nodes.map(n => ({ id: n.id, currentPos: n.position })))
    return nodes.map(node => {
      if (savedPositions[node.id]) {
        console.log(`Applying saved position to node ${node.id}:`, savedPositions[node.id])
        return { ...node, position: savedPositions[node.id] }
      }
      return node
    })
  }, [])

  // Function to flatten nested network data structure
  const flattenNetworkData = useCallback((networkData) => {
    console.log('🔧 flattenNetworkData called with:', networkData)
    console.log('🔧 networkData type:', typeof networkData)
    console.log('🔧 networkData is array:', Array.isArray(networkData))

    const flatArray = []
    const seenIds = new Set() // Track seen node IDs to prevent duplicates

    function flattenNode(node) {
      console.log('🔧 Processing node:', node)
      if (!node || !node.id) {
        console.log('🔧 Skipping node - no id:', node)
        return
      }

      // Skip if we've already processed this node ID
      if (seenIds.has(node.id)) {
        console.log(`🔧 Skipping duplicate node with ID: ${node.id}`)
        return
      }

      seenIds.add(node.id)
      console.log(`🔧 Adding node ${node.id} to flat array`)

      // Add the current node to flat array (without children property to avoid confusion)
      const flatNode = { ...node }
      delete flatNode.children // Remove children property since we'll use parent_id relationships
      flatArray.push(flatNode)

      // Process children recursively
      if (node.children && Array.isArray(node.children)) {
        console.log(`🔧 Processing ${node.children.length} children for node ${node.id}`)
        node.children.forEach(child => {
          flattenNode(child)
        })
      }
    }

    if (Array.isArray(networkData)) {
      console.log(`🔧 Processing array with ${networkData.length} items`)
      networkData.forEach(node => flattenNode(node))
    } else {
      console.log('🔧 Processing single object')
      flattenNode(networkData)
    }

    console.log('🔧 Final flattened array length:', flatArray.length)
    console.log('🔧 Flattened network data:', flatArray)
    return flatArray
  }, [])

  // Function to convert backend network data to React Flow format
  const convertNetworkToFlow = useCallback((networkData, personalData = null) => {
    const nodes = []
    const edges = []

    console.log('Converting network data:', networkData)
    console.log('Personal data:', personalData)

    if (!networkData) {
      console.warn('Network data is null or undefined')
      return { nodes, edges }
    }

    // First, flatten the nested structure to a flat array
    const dataArray = flattenNetworkData(networkData)

    // Process personal data to determine which nodes are mine and get contracts info
    let personalNodes = []
    if (personalData && Array.isArray(personalData)) {
      personalNodes = personalData
      console.log('Personal nodes found:', personalNodes)
    }

    // Mark nodes as mine based on personal data and calculate contracts count
    dataArray.forEach(node => {
      if (node && node.id) {
        // Check if this node is in personal data (means it's mine)
        const personalNode = personalNodes.find(pNode => pNode && pNode.id === node.id)
        if (personalNode) {
          node.is_mine = true
          node.parent_master_user_id = personalNode.parent_master_user_id
          node.contract_id = personalNode.contract_id
          console.log(`Node ${node.id} marked as mine with owner ${personalNode.parent_master_user_id} and contract ${personalNode.contract_id}`)

          // Calculate contracts count from children's contracts
          if (personalNode.children && Array.isArray(personalNode.children)) {
            let totalContractsCount = 0
            personalNode.children.forEach(child => {
              if (child && child.contracts && Array.isArray(child.contracts)) {
                const pendingContracts = child.contracts.filter(contract =>
                  contract && contract.status_id === 1
                )
                totalContractsCount += pendingContracts.length
              }
            })
            node.contracts_count = totalContractsCount
            console.log(`Node ${node.id} contracts count: ${totalContractsCount}`)
          }
        } else {
          // Check if this node is a child in personal data
          let foundAsChild = false
          personalNodes.forEach(pNode => {
            if (pNode && pNode.children && Array.isArray(pNode.children)) {
              const childNode = pNode.children.find(child => child && child.id === node.id)
              if (childNode) {
                foundAsChild = true
                node.is_mine = false // Child nodes are not "mine" but we need their contracts count

                // Calculate contracts count for this child node
                if (childNode.contracts && Array.isArray(childNode.contracts)) {
                  const pendingContracts = childNode.contracts.filter(contract =>
                    contract && contract.status_id === 1
                  )
                  node.contracts_count = pendingContracts.length
                  console.log(`Child node ${node.id} contracts count: ${pendingContracts.length}`)
                } else {
                  node.contracts_count = 0
                }

                // Add contract and user info from personal data
                // Check both single contract and contracts array
                let activeContract = null
                if (childNode.contract) {
                  activeContract = childNode.contract
                } else if (childNode.contracts && Array.isArray(childNode.contracts) && childNode.contracts.length > 0) {
                  // Find active contract (status_id = 2) or use first contract
                  activeContract = childNode.contracts.find(contract => contract && contract.status_id === 2) || childNode.contracts[0]
                }

                if (activeContract) {
                  node.contract_status_id = activeContract.status_id
                  node.node_user = activeContract.user || null
                  // Add contracts array to node_user for FireWorkerModal
                  if (node.node_user && childNode.contracts) {
                    node.node_user.contracts = childNode.contracts
                  }
                  console.log(`Child node ${node.id} contract status: ${activeContract.status_id}, user:`, activeContract.user)
                  console.log(`Child node ${node.id} contracts array:`, childNode.contracts)
                }
              }
            }
          })

          if (!foundAsChild) {
            node.is_mine = false
            node.contracts_count = 0
          }
        }
      }
    })

    console.log('🔍 Network data received:', networkData)
    console.log('📊 Flattened data array:', dataArray)
    console.log('👤 User owned nodes:', dataArray.filter(node => node && node.is_mine))

    if (dataArray.length === 0) {
      console.warn('Network data array is empty after flattening')
      return { nodes, edges }
    }

    console.log('Working with flattened data:', dataArray)

    // Create a map for quick lookup of nodes by ID
    const nodeMap = new Map()
    dataArray.forEach(node => {
      nodeMap.set(node.id, node)
    })

    // Build parent-child relationships from the flat array using parent_id
    const childrenMap = new Map()
    dataArray.forEach(node => {
      // Add null check for node and parent_id property
      if (node && typeof node === 'object' && node.hasOwnProperty('parent_id') && node.parent_id !== null) {
        if (!childrenMap.has(node.parent_id)) {
          childrenMap.set(node.parent_id, [])
        }
        childrenMap.get(node.parent_id).push(node)
      }
    })

    // Sort children by created_at (oldest first - left to right, newest on the right)
    childrenMap.forEach((children) => {
      children.sort((a, b) => new Date(a.created_at) - new Date(b.created_at))
    })

    console.log('Children map built:', childrenMap)

    // Find all root nodes (nodes with parent_id === null OR parent not in the data)
    const rootNodes = dataArray.filter(node => {
      if (!node || typeof node !== 'object') return false

      // If parent_id is null, it's definitely a root
      if (node.parent_id === null) return true

      // If parent_id exists but the parent is not in our data, treat as root
      const parentExists = dataArray.some(n => n && n.id === node.parent_id)
      if (!parentExists) {
        console.log(`🌳 Node ${node.id} (${node.position}) has parent_id ${node.parent_id} but parent not found in data - treating as root`)
        return true
      }

      return false
    })

    console.log('🌳 Found root nodes:', rootNodes.map(n => ({ id: n.id, position: n.position, parent_id: n.parent_id })))

    if (rootNodes.length === 0) {
      console.warn('No root nodes found in network data array')
      return { nodes, edges }
    }

    console.log('Found root nodes:', rootNodes)

    // ZERO LINE CROSSINGS ALGORITHM - Layered Tree Layout
    const processedNodes = new Set()
    const nodePositions = new Map()
    const levelNodes = new Map() // Track nodes by level

    // First pass: collect all nodes by level and add "+" nodes
    function collectNodesByLevel(networkNode, level = 0) {
      if (!networkNode || processedNodes.has(networkNode.id)) {
        return
      }

      processedNodes.add(networkNode.id)

      if (!levelNodes.has(level)) {
        levelNodes.set(level, [])
      }
      levelNodes.get(level).push(networkNode)

      const children = childrenMap.get(networkNode.id) || []

      // If this node is owned by user, add a virtual "+" node as its last child
      // Sort children by creation date first, then add the + node at the end
      // But only if the node has less than 10 children
      if (networkNode.is_mine) {
        console.log(`🔥 Checking if + node should be added for owned node ${networkNode.id} (${networkNode.position})`)

        // Sort existing children by creation date (oldest first)
        children.sort((a, b) => {
          const dateA = new Date(a.created_at || '1970-01-01')
          const dateB = new Date(b.created_at || '1970-01-01')
          return dateA - dateB
        })

        // Only add + node if there are less than 10 children
        if (children.length < 10) {
          const addNode = {
            id: `add-${networkNode.id}`,
            position: '+',
            parent_id: networkNode.id,
            is_mine: false,
            pointer_status_id: 1,
            created_at: new Date().toISOString(), // Make it the newest
            isAddNode: true // Mark as add node
          }
          children.push(addNode) // Add + node at the very end

          // IMPORTANT: Update childrenMap to include the addNode so edges can be created
          childrenMap.set(networkNode.id, children)

          console.log(`✅ + node added for ${networkNode.id}, total children: ${children.length}`)
        } else {
          console.log(`🚫 + node NOT added for ${networkNode.id} - already has ${children.length} children (max 10)`)
        }
      } else {
        console.log(`❌ Node ${networkNode.id} (${networkNode.position}) is not owned by user (is_mine: ${networkNode.is_mine})`)
      }

      children.forEach(child => {
        collectNodesByLevel(child, level + 1)
      })
    }

    // Collect all nodes by level first - process all root nodes
    rootNodes.forEach(rootNode => {
      collectNodesByLevel(rootNode)
    })
    processedNodes.clear() // Reset for second pass

    // Second pass: position nodes level by level to guarantee no crossings
    function buildHierarchy() {
      const levelHeight = 500 // Large vertical spacing between levels
      const nodeWidth = 280 // Horizontal spacing between nodes (increased for fixed width cards)
      const createdNodes = new Set() // Track created nodes to prevent duplicates

      // Process each level
      for (const [level, nodesAtLevel] of levelNodes) {
        const y = 100 + level * levelHeight
        const totalWidth = (nodesAtLevel.length - 1) * nodeWidth
        const startX = 1000 - totalWidth / 2 // Center the level

        nodesAtLevel.forEach((networkNode, index) => {
          if (processedNodes.has(networkNode.id)) {
            return // Skip if already processed
          }

          // Check for duplicate node creation
          const nodeId = String(networkNode.id)
          if (createdNodes.has(nodeId)) {
            console.warn(`Duplicate node detected and skipped: ${nodeId}`)
            return
          }

          processedNodes.add(networkNode.id)
          createdNodes.add(nodeId)

          const x = startX + index * nodeWidth
          const position = { x, y }
          nodePositions.set(networkNode.id, position)

          console.log(`Level ${level}: Positioning ${networkNode.position} at (${x}, ${y})`)

          // Check if this is an add node
          if (networkNode.isAddNode) {
            console.log(`🎯 Creating + node for parent ${networkNode.parent_id} at position (${position.x}, ${position.y})`)
            // Create add node with higher z-index to appear above other nodes
            const addNode = {
              id: String(networkNode.id),
              type: 'addNode',
              position,
              zIndex: 1000, // High z-index for add nodes to appear on top
              data: {
                parentId: networkNode.parent_id,
                label: '+'
              }
            }
            nodes.push(addNode)
            console.log(`✅ + node created with ID: ${addNode.id}`)
          } else {
            // Create regular React Flow node with appropriate z-index
            const node = {
              id: String(networkNode.id),
              type: 'custom',
              position,
              zIndex: 100, // Lower z-index for regular nodes
              data: {
                label: networkNode.position || networkNode.slug || `Node ${networkNode.id}`,
                isEditing: false,
                children: childrenMap.get(networkNode.id) || [],
                description: networkNode.description || '',
                reward: networkNode.reward || 0,
                is_mine: networkNode.is_mine || false,
                pointer_status_id: networkNode.pointer_status_id || 1,
                parent_id: networkNode.parent_id,
                contracts_count: networkNode.contracts_count || 0,
                node_user: networkNode.node_user || null,
                contract_status_id: networkNode.contract_status_id || null,
                parent_master_user_id: networkNode.parent_master_user_id || null,
                contract_id: networkNode.contract_id || null,
                contracts_exists: networkNode.contracts_exists || false,
                // Add reference to all network nodes for parent checking
                allNodes: dataArray
              }
            }
            nodes.push(node)
          }
        })
      }

      // Third pass: create edges after all nodes are positioned
      const createdEdges = new Set() // Track created edges to prevent duplicates
      for (const [, nodesAtLevel] of levelNodes) {
        nodesAtLevel.forEach(networkNode => {
          // Skip add nodes for edge creation from them
          if (networkNode.isAddNode) return

          const children = childrenMap.get(networkNode.id) || []
          children.forEach(child => {
            // Create unique edge ID to prevent duplicates
            const edgeId = `edge-${networkNode.id}-${child.id}`

            // Skip if this edge was already created
            if (createdEdges.has(edgeId)) {
              console.warn(`Duplicate edge detected and skipped: ${edgeId}`)
              return
            }

            createdEdges.add(edgeId)

            // Create smooth curved edge - beautiful rounded appearance
            const edgeStyle = child.isAddNode ? {
              stroke: '#10b981',
              strokeWidth: 2,
              strokeLinecap: 'round',
              strokeLinejoin: 'round',
              strokeDasharray: '5,5' // Dashed line for add nodes
            } : {
              stroke: '#10b981', // Green color
              strokeWidth: 2,
              strokeLinecap: 'round',
              strokeLinejoin: 'round',
              borderRadius: '8px', // Add rounded corners
            }

            edges.push({
              id: edgeId,
              source: String(networkNode.id),
              target: String(child.id),
              type: 'smoothstep', // Always use smoothstep for beautiful curves
              style: edgeStyle,
              data: {
                isActive: true // Enable animations for all edges
              }
            })
          })
        })
      }
    }

    // Start building with layered layout - NO LINE CROSSINGS GUARANTEED
    buildHierarchy()

    // Validate for duplicate IDs before returning
    const nodeIds = nodes.map(n => n.id)
    const edgeIds = edges.map(e => e.id)
    const duplicateNodeIds = nodeIds.filter((id, index) => nodeIds.indexOf(id) !== index)
    const duplicateEdgeIds = edgeIds.filter((id, index) => edgeIds.indexOf(id) !== index)

    if (duplicateNodeIds.length > 0) {
      console.error('Duplicate node IDs found:', duplicateNodeIds)
    }
    if (duplicateEdgeIds.length > 0) {
      console.error('Duplicate edge IDs found:', duplicateEdgeIds)
    }

    console.log(`Conversion complete: ${nodes.length} nodes, ${edges.length} edges`)
    console.log('Final nodes:', nodes.map(n => ({ id: n.id, label: n.data.label })))
    console.log('Final edges:', edges.map(e => ({ id: e.id, source: e.source, target: e.target })))
    return { nodes, edges }
  }, [])

  // Load network data from backend
  const loadNetworkData = useCallback(async () => {
    if (!user) {
      console.log('No user, skipping network load')
      return
    }

    console.log('Loading network data for user:', user)

    try {
      setIsLoading(true)

      // Try to load from API first
      let response
      try {
        response = await api.network.getNetwork()
        console.log('Network API response:', response)
      } catch (apiError) {
        console.warn('API call failed:', apiError)
        throw apiError
      }

      // Validate response before converting
      if (response) {
        console.log('Network response type:', typeof response, 'value:', response)

        // New API structure: response has 'data' (general info) and 'personal' (user-specific info)
        let networkData = null
        let personalData = null

        if (response.data) {
          networkData = response.data
          personalData = response.personal || null
          console.log('New API structure detected - data:', networkData, 'personal:', personalData)
        } else {
          // Fallback for old API structure
          networkData = response
          console.log('Old API structure detected')
        }

        // Validate network data
        let nodeCount = 0
        if (Array.isArray(networkData)) {
          nodeCount = networkData.length
          if (nodeCount === 0) {
            console.log('Network data is empty array - showing "network not found"')
            setNodes([])
            setEdges([])
            return
          }
        } else if (typeof networkData === 'object' && networkData.id) {
          nodeCount = 1 // At least the root node
        } else {
          console.log('Network data is not valid - showing "network not found"')
          setNodes([])
          setEdges([])
          return
        }

        console.log(`Processing network data with ${nodeCount} root node(s)`)

        // Convert to React Flow format with personal data
        const { nodes: flowNodes, edges: flowEdges } = convertNetworkToFlow(networkData, personalData)

        console.log('Converted nodes:', flowNodes.length, 'edges:', flowEdges.length)
        console.log('Flow nodes:', flowNodes)
        console.log('Flow edges:', flowEdges)

        // Debug: Check if conversion worked
        if (flowNodes.length === 0) {
          console.error('❌ convertNetworkToFlow returned 0 nodes!')
          console.error('Original response:', response)
          console.error('Response type:', typeof response)
          console.error('Response is array:', Array.isArray(response))
          if (Array.isArray(response)) {
            console.error('Response length:', response.length)
            console.error('First item:', response[0])
          }
        }

        if (flowNodes.length > 0) {
          // შევინახოთ საწყისი პოზიციები
          const originalPos = {}
          flowNodes.forEach(node => {
            originalPos[node.id] = { ...node.position }
          })
          setOriginalPositions(originalPos)

          // Load saved positions from localStorage
          const nodesWithSavedPositions = loadSavedPositions(flowNodes)

          setNodes(nodesWithSavedPositions)
          setEdges(flowEdges)

          // Update store with the new nodes data
          setStoreNodes(nodesWithSavedPositions)
          setStoreEdges(flowEdges)

          toast.success(`Network data loaded: ${flowNodes.length} nodes, ${flowEdges.length} edges`)
        } else {
          console.log('No nodes created from network data - showing "network not found"')
          setNodes([])
          setEdges([])

          // Update store with empty data
          setStoreNodes([])
          setStoreEdges([])
        }
      } else {
        console.log('Network response is empty or null - showing "network not found"')
        setNodes([])
        setEdges([])

        // Update store with empty data
        setStoreNodes([])
        setStoreEdges([])
      }

    } catch (error) {
      console.error('Failed to load network data:', error)
      // Don't show any fallback data, just set empty state
      setNodes([])
      setEdges([])

      // Update store with empty data
      setStoreNodes([])
      setStoreEdges([])
    } finally {
      setIsLoading(false)
    }
  }, [user, convertNetworkToFlow, storeNodes, storeEdges])



  // Load data on component mount and when user changes
  useEffect(() => {
    if (user) {
      console.log('User changed, attempting to load network data')
      loadNetworkData()
    } else {
      console.log('No user, clearing diagram')
      setNodes([])
      setEdges([])

      // Update store with empty data
      setStoreNodes([])
      setStoreEdges([])

      setIsLoading(false)
    }
  }, [user]) // Simplified dependency

  // Load user diagram on mount - ეს useEffect წავშალოთ რადგან loadNetworkData უკვე აკეთებს ამას

  // ეს useEffect-იც წავშალოთ რადგან loadNetworkData უკვე აკეთებს ამას

  // Auto-save diagram every 5 seconds
  useEffect(() => {
    if (!user?.id) return

    const autoSaveInterval = setInterval(() => {
      console.log('Auto-saving diagram...')
      saveUserDiagram(user.id)
    }, 5000)

    return () => clearInterval(autoSaveInterval)
  }, [user?.id, saveUserDiagram])

  // Sync with store changes
  useEffect(() => {
    console.log('Store state changed - nodes:', storeNodes.length, 'edges:', storeEdges.length)
    if (storeNodes.length > 0 || storeEdges.length > 0) {
      const storeNodesWithSavedPositions = loadSavedPositions(storeNodes)
      setNodes(storeNodesWithSavedPositions)
      setEdges(storeEdges)
    }
  }, [storeNodes, storeEdges, loadSavedPositions])

  // Listen for network data updates
  useEffect(() => {
    const handleNetworkUpdate = () => {
      console.log('Network data updated, reloading...')
      if (user) {
        loadNetworkData()
      }
    }

    window.addEventListener('networkDataUpdated', handleNetworkUpdate)
    return () => window.removeEventListener('networkDataUpdated', handleNetworkUpdate)
  }, [user, loadNetworkData])

  const onConnect = useCallback(
    () => {
      // Prevent manual connections - connections are only created through the add node functionality
      return
    },
    []
  )



  // Create stable nodeTypes object that doesn't change on every render
  const nodeTypes = useMemo(() => ({
    custom: (props) => <CustomNode {...props} />,
    addNode: (props) => <AddNode {...props} />
  }), []) // Empty dependency array - this object never changes

  // Create stable edgeTypes object for custom edges
  const edgeTypes = useMemo(() => ({
    moving: CustomEdge,
    smoothstep: SafeSmoothStepEdge, // Use safe wrapper for smoothstep
    straight: SafeStraightEdge // Use safe wrapper for straight
  }), [])

  // Custom onNodesChange to handle node position updates and save to localStorage
  const handleNodesChange = useCallback(
    (changes) => {
      // უბრალოდ გადავცეთ ცვლილებები ReactFlow-ს
      onNodesChange(changes)

      // მხოლოდ პოზიციის ცვლილებები შევინახოთ localStorage-ში
      changes.forEach(change => {
        if (change.type === 'position' && change.dragging === false && change.position) {
          // შევინახოთ პოზიცია localStorage-ში
          const savedPositions = JSON.parse(localStorage.getItem('flowmind-node-positions') || '{}')
          savedPositions[change.id] = change.position
          localStorage.setItem('flowmind-node-positions', JSON.stringify(savedPositions))
        }
      })
    },
    [onNodesChange]
  )

  const handleReset = useCallback(() => {
    // წავშალოთ შენახული პოზიციები localStorage-დან
    localStorage.removeItem('flowmind-node-positions')

    // დავაბრუნოთ ნოუდები საწყის პოზიციებზე
    const resetNodes = nodes.map(node => ({
      ...node,
      position: originalPositions[node.id] || node.position
    }))

    // განვაახლოთ ნოუდები
    setNodes(resetNodes)

    toast.success("ნოუდები დაბრუნდა საწყის პოზიციაზე")
  }, [nodes, setNodes, originalPositions])

  // Open my node modal
  const handleOpenMyNodeModal = useCallback(() => {
    const myNode = nodes.find(node => node.data && node.data.is_mine)
    if (myNode) {
      setMyNodeModalOpen(true)
    } else {
      toast.error("ჩემი ნოუდი ვერ მოიძებნა")
    }
  }, [nodes])



  // Start continuous pulse animation when component loads
  useEffect(() => {
    if (nodes.length > 0 && edges.length > 0) {
      console.log('Starting continuous pulse animation - nodes:', nodes.length, 'edges:', edges.length, 'animations enabled:', edgeAnimationsEnabled)

      // Add CSS class to the ReactFlow container for global node animation
      const reactFlowElement = document.querySelector('.react-flow')
      if (reactFlowElement) {
        if (edgeAnimationsEnabled) {
          reactFlowElement.classList.add('pulse-animation-active')
        } else {
          reactFlowElement.classList.remove('pulse-animation-active')
        }
      }

      // All edges are already using MovingEdge with animation controlled by edgeAnimationsEnabled
    }
  }, [nodes.length, edges.length, edgeAnimationsEnabled])

  const proOptions = { hideAttribution: true }

  if (isLoading) {
    return (
      <div className="w-full h-full bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading your diagram...</p>
        </div>
      </div>
    )
  }

  // Show "Network not found" message when no nodes are available
  if (nodes.length === 0) {
    return (
      <div className="w-full h-full bg-background flex items-center justify-center">
        <div className="text-center space-y-4">
          <p className="text-lg text-muted-foreground">ქსელი ვერ მოიძებნა</p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full h-full bg-background relative z-0">

      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={handleNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        onNodeDragStop={(_, node) => {
          // უბრალოდ შევინახოთ პოზიცია localStorage-ში
          const savedPositions = JSON.parse(localStorage.getItem('flowmind-node-positions') || '{}')
          savedPositions[node.id] = node.position
          localStorage.setItem('flowmind-node-positions', JSON.stringify(savedPositions))
        }}
        nodeTypes={nodeTypes}
        edgeTypes={edgeTypes}
        defaultEdgeOptions={defaultEdgeOptions}
        connectionLineType={ConnectionLineType.SmoothStep}
        fitView
        fitViewOptions={{
          padding: 0.3, // More padding around the diagram for better visibility
          includeHiddenNodes: false,
          minZoom: 0.02, // Allow zooming out much more to see very large diagrams
          maxZoom: 1.5, // Reasonable zoom in limit
        }}
        minZoom={0.02}
        maxZoom={2}
        proOptions={proOptions}
        className="bg-background"
        // Disable edge interactions but allow edge display
        edgesUpdatable={false}
        edgesFocusable={true}
        // Enable node dragging but prevent manual connections
        nodesDraggable={true}
        nodesConnectable={false}
        nodesFocusable={true}
      >
        <Background
          color="hsl(var(--muted-foreground))"
          gap={50}
          size={1}
          className="opacity-20"
        />
        
        <Controls 
          className="bg-background border border-border rounded-lg shadow-lg"
          showInteractive={false}
        />
        
        {/* <MiniMap 
          className="bg-background border border-border rounded-lg shadow-lg"
          nodeColor="hsl(var(--primary))"
          maskColor="hsl(var(--muted) / 0.8)"
        /> */}

        <Panel position="top-right" className="flex items-center space-x-2">
          <AnimationToggle />
          <ApplicantsButton nodes={nodes} />
          <MyNodeControls nodes={nodes} onOpenModal={handleOpenMyNodeModal} />
          <Button
            onClick={handleReset}
            variant="outline"
            size="sm"
            className="shadow-lg"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            Reset
          </Button>
        </Panel>

        {/* Status Indicator Panel */}
        <Panel position="top-left" className="p-3 bg-background/80 backdrop-blur-sm border border-border rounded-lg shadow-lg">
          <div className="flex flex-col space-y-2">
            <div className="text-xs font-medium text-muted-foreground mb-1">Network Status</div>
            <div className="flex items-center space-x-2">
              <StatusIndicator statusId={1} />
              <span className="text-xs text-muted-foreground">Active</span>
            </div>
            <div className="flex items-center space-x-2">
              <StatusIndicator statusId={2} />
              <span className="text-xs text-muted-foreground">Warning</span>
            </div>
            <div className="flex items-center space-x-2">
              <StatusIndicator statusId={3} />
              <span className="text-xs text-muted-foreground">Error</span>
            </div>
          </div>
        </Panel>
      </ReactFlow>

      {/* My Node Modal */}
      {(() => {
        const myNode = nodes.find(node => node.data && node.data.is_mine)
        if (!myNode) return null

        // Find children with node_user info for my node
        const childrenWithUsers = myNode.data.children?.filter(child =>
          child.node_user && child.node_user.id && child.pointer_status_id !== 3
        ) || []

        console.log('My node children with users:', childrenWithUsers)

        return (
          <NodeTasksModal
            isOpen={myNodeModalOpen}
            onOpenChange={setMyNodeModalOpen}
            nodeId={myNode.id}
            nodeLabel={myNode.data.label}
            isUserOwnNode={true}
            nodeStatusId={myNode.data.pointer_status_id}
            nodeUser={myNode.data.node_user}
            nodeContractStatusId={myNode.data.contract_status_id}
            childrenWithUsers={childrenWithUsers}
            nodeOwnerInfo={myNode.data}
          />
        )
      })()}
    </div>
  )
}
