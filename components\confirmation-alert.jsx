"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { CheckCircle, X, AlertTriangle, Loader2 } from "lucide-react"

export function ConfirmationAlert({
  isOpen,
  onClose,
  onConfirm,
  title = "დადასტურება",
  message,
  confirmText = "დადასტურება",
  cancelText = "გაუქმება",
  type = "default", // default, warning, success, danger
  isLoading = false
}) {
  if (!isOpen) return null

  const getTypeStyles = () => {
    switch (type) {
      case 'warning':
        return {
          bg: 'bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20',
          border: 'border-yellow-200 dark:border-yellow-800',
          icon: <AlertTriangle className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />,
          confirmBtn: 'bg-yellow-600 hover:bg-yellow-700 text-white'
        }
      case 'success':
        return {
          bg: 'bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20',
          border: 'border-green-200 dark:border-green-800',
          icon: <CheckCircle className="w-6 h-6 text-green-600 dark:text-green-400" />,
          confirmBtn: 'bg-green-600 hover:bg-green-700 text-white'
        }
      case 'danger':
        return {
          bg: 'bg-gradient-to-br from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20',
          border: 'border-red-200 dark:border-red-800',
          icon: <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />,
          confirmBtn: 'bg-red-600 hover:bg-red-700 text-white'
        }
      default:
        return {
          bg: 'bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20',
          border: 'border-blue-200 dark:border-blue-800',
          icon: <CheckCircle className="w-6 h-6 text-blue-600 dark:text-blue-400" />,
          confirmBtn: 'bg-blue-600 hover:bg-blue-700 text-white'
        }
    }
  }

  const styles = getTypeStyles()

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm animate-in fade-in-0 duration-200"
      onClick={onClose}
    >
      <div
        className={`
          relative w-full max-w-md mx-auto rounded-xl border shadow-2xl
          ${styles.bg} ${styles.border}
          animate-in zoom-in-95 slide-in-from-bottom-4 duration-300
        `}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Close button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
          disabled={isLoading}
        >
          <X className="w-4 h-4 text-muted-foreground" />
        </button>

        <div className="p-6">
          {/* Header */}
          <div className="flex items-center gap-3 mb-4">
            {styles.icon}
            <h3 className="text-lg font-semibold text-foreground">
              {title}
            </h3>
          </div>

          {/* Message */}
          <div className="mb-6">
            <p className="text-sm text-muted-foreground leading-relaxed whitespace-pre-line">
              {message}
            </p>
          </div>

          {/* Actions */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
              disabled={isLoading}
            >
              {cancelText}
            </Button>
            <Button
              onClick={onConfirm}
              className={`flex-1 ${styles.confirmBtn}`}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  იტვირთება...
                </>
              ) : (
                confirmText
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}
