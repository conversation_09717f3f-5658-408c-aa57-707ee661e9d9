@import "tailwindcss";
@import "tw-animate-css";

/* Line clamp utilities */
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Prose styles for rich text content */
.prose {
  color: var(--foreground);
  max-width: none;
}

.prose strong {
  font-weight: 600;
  color: var(--foreground);
}

.prose em {
  font-style: italic;
  color: var(--foreground);
}

.prose u {
  text-decoration: underline;
}

.prose ul, .prose ol {
  margin: 0.5rem 0;
  padding-left: 1.5rem;
}

.prose ul {
  list-style-type: disc;
}

.prose ol {
  list-style-type: decimal;
}

.prose li {
  margin: 0.25rem 0;
}

.prose blockquote {
  border-left: 4px solid hsl(var(--border));
  padding-left: 1rem;
  margin: 0.5rem 0;
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

.prose p {
  margin: 0.5rem 0;
}

.prose p:first-child {
  margin-top: 0;
}

.prose p:last-child {
  margin-bottom: 0;
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --sky-500: 14 165 233;
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .animate-in {
    animation: animate-in 0.5s ease-out;
  }

  .fade-in {
    animation: fade-in 0.6s ease-out;
  }

  .delay-300 {
    animation-delay: 300ms;
  }
}

@keyframes animate-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Pulse Animation Styles */
@keyframes pulse-node {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.8);
    opacity: 1;
  }
  25% {
    transform: scale(1.05);
    box-shadow: 0 0 0 8px rgba(16, 185, 129, 0.6);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 0 15px rgba(16, 185, 129, 0.4);
    opacity: 1;
  }
  75% {
    transform: scale(1.05);
    box-shadow: 0 0 0 25px rgba(16, 185, 129, 0.2);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 35px rgba(16, 185, 129, 0);
    opacity: 1;
  }
}

@keyframes pulse-border {
  0% {
    border-color: #3b82f6;
    box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.8);
    opacity: 1;
  }
  25% {
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.6);
    opacity: 1;
  }
  50% {
    border-color: #10b981;
    box-shadow: 0 0 0 6px rgba(16, 185, 129, 0.4);
    opacity: 1;
  }
  75% {
    border-color: #059669;
    box-shadow: 0 0 0 9px rgba(16, 185, 129, 0.2);
    opacity: 1;
  }
  100% {
    border-color: #3b82f6;
    box-shadow: 0 0 0 12px rgba(16, 185, 129, 0);
    opacity: 1;
  }
}

.pulse-node-active {
  animation: pulse-node 3s ease-in-out infinite;
  z-index: 10 !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

.pulse-node-active > div {
  animation: pulse-border 3s ease-in-out infinite;
  opacity: 1 !important;
  visibility: visible !important;
}

.pulse-node-active .bg-card {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Edge styling for beautiful rounded corners - always like pulse animation */
.react-flow__edge path {
  stroke: #10b981 !important; /* Always green color */
  stroke-width: 2 !important; /* Consistent width */
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
  fill: none !important;
}

/* Smooth transitions for edges */
.react-flow__edge {
  transition: none !important; /* Remove transitions during pulse animation */
}

/* Ensure moving edges maintain rounded appearance */
.react-flow__edge.moving path {
  stroke: #10b981 !important; /* Keep green color during animation */
  stroke-width: 2 !important; /* Keep consistent width */
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
}

/* Make all edges look beautiful like during pulse animation */
.react-flow__edges {
  filter: none !important;
}

/* Ensure smoothstep edges are always used */
.react-flow__edge-smoothstep path {
  stroke: #10b981 !important;
  stroke-width: 2 !important;
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
}

/* Force all SVG paths to have rounded line caps */
svg path {
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
}

/* Specific styling for our custom edges */
.react-flow__edge g path {
  stroke: #10b981 !important;
  stroke-width: 2 !important;
  stroke-linecap: round !important;
  stroke-linejoin: round !important;
  fill: none !important;
}

/* Ensure add nodes (+ buttons) always appear on top */
.react-flow__node[data-id*="add-"] {
  z-index: 1000 !important;
}

/* Regular nodes have lower z-index */
.react-flow__node:not([data-id*="add-"]) {
  z-index: 100 !important;
}

/* When a node is being dragged, give it highest priority */
.react-flow__node.dragging {
  z-index: 1500 !important;
}

/* Ensure modals and dialogs appear above everything */
[data-slot="dialog-overlay"] {
  z-index: 10000 !important;
}

[data-slot="dialog-content"] {
  z-index: 10001 !important;
}

/* Ensure notifications dropdown has proper z-index but below modals */
.notifications-dropdown {
  z-index: 9999 !important;
}

/* Ensure main content has proper stacking context */
main {
  position: relative;
  z-index: 0;
}

/* Ensure header has higher z-index than main content */
header {
  position: relative;
  z-index: 50;
}

/* Force notifications dropdown to appear above everything except modals */
.notifications-dropdown {
  position: fixed !important;
  z-index: 9999 !important;
}
