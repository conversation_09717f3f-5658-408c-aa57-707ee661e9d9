"use client"

import { createContext, useContext, useEffect, useState } from 'react'
import { api, tokenManager } from '@/lib/api'

const AuthContext = createContext({})

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const initAuth = async () => {
      if (tokenManager.isAuthenticated()) {
        try {
          // For now, we'll skip the getCurrentUser call since the backend might not have this endpoint
          // const userData = await api.auth.getCurrentUser()
          // setUser(userData.data || userData)

          // Instead, we'll set a basic user object if token exists, including stored user ID
          const userId = tokenManager.getUserId()
          setUser({
            authenticated: true,
            id: userId
          })
        } catch (error) {
          // Token might be invalid, clear it
          console.warn('Failed to get current user:', error)
          tokenManager.removeToken()
          setUser(null)
        }
      }
      setLoading(false)
    }

    initAuth()
  }, [])

  const login = async (credentials) => {
    const response = await api.auth.login(credentials)
    if (response.success && response.data) {
      // Set user data from response, include user ID
      const userData = response.data.user || {
        email: credentials.email,
        id: tokenManager.getUserId()
      }
      setUser(userData)
    }
    return response
  }

  const register = async (userData) => {
    const response = await api.auth.register(userData)
    if (response.success && response.data) {
      // Set user data from response, include user ID
      const userDataWithId = response.data.user || {
        email: userData.email,
        name: userData.name,
        id: tokenManager.getUserId()
      }
      setUser(userDataWithId)
    }
    return response
  }

  const logout = async () => {
    await api.auth.logout()
    setUser(null)
  }

  const value = {
    user,
    loading,
    login,
    register,
    logout,
    isAuthenticated: !!user
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}
