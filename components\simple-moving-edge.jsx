"use client";

import React, { useRef, useEffect } from 'react';
import useStore from '@/lib/store';

export const SimpleMovingEdge = React.memo(function SimpleMovingEdge({
  sourceX,
  sourceY,
  targetX,
  targetY,
  isActive = false,
  duration = 2000,
  className = "",
  strokeWidth = 2,
  stroke = "#10b981"
}) {
  const { edgeAnimationsEnabled } = useStore();
  const canvasRef = useRef(null);
  const animationRef = useRef(null);

  useEffect(() => {
    console.log('SimpleMovingEdge useEffect:', { isActive, edgeAnimationsEnabled, canvasRef: !!canvasRef.current });

    if (!isActive || !edgeAnimationsEnabled) {
      console.log('Early return from SimpleMovingEdge - animations disabled');
      return;
    }

    // Use setTimeout to ensure canvas is mounted
    const timeoutId = setTimeout(() => {
      if (!canvasRef.current) {
        console.log('Canvas ref not available');
        return;
      }

      const canvas = canvasRef.current;

      // Check if it's actually a canvas element
      if (!canvas || typeof canvas.getContext !== 'function') {
        console.log('Canvas ref is not a canvas element:', canvas);
        return;
      }

      console.log('Starting animation in SimpleMovingEdge');
      const ctx = canvas.getContext('2d');
    
    // Calculate canvas bounds
    const padding = 50;
    const minX = Math.min(sourceX, targetX) - padding;
    const maxX = Math.max(sourceX, targetX) + padding;
    const minY = Math.min(sourceY, targetY) - padding;
    const maxY = Math.max(sourceY, targetY) + padding;
    const width = maxX - minX;
    const height = maxY - minY;
    
    // Convert to local coordinates
    const localSourceX = sourceX - minX;
    const localSourceY = sourceY - minY;
    const localTargetX = targetX - minX;
    const localTargetY = targetY - minY;
    
    // Calculate path
    const deltaY = targetY - sourceY;
    const controlPointOffset = Math.abs(deltaY) * 0.5;
    const approximateLength = Math.sqrt((targetX - sourceX) ** 2 + (targetY - sourceY) ** 2) * 1.2;
    
    // Set canvas size
    canvas.width = width;
    canvas.height = height;
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;
    
    let particleOffset = 0;
    const speed = approximateLength / duration;
    
    // Bezier curve calculation
    const getBezierPoint = (t) => {
      const oneMinusT = 1 - t;
      const tSquared = t * t;
      const oneMinusTSquared = oneMinusT * oneMinusT;
      
      return {
        x: oneMinusTSquared * localSourceX + 
           2 * oneMinusT * t * localSourceX + 
           tSquared * localTargetX,
        y: oneMinusTSquared * localSourceY + 
           2 * oneMinusT * t * (localSourceY + controlPointOffset) + 
           tSquared * localTargetY
      };
    };
    
    let lastTime = performance.now();
    
    const animate = (currentTime) => {
      if (!isActive || !edgeAnimationsEnabled) {
        ctx.clearRect(0, 0, width, height);
        return;
      }
      
      const deltaTime = currentTime - lastTime;
      lastTime = currentTime;
      
      // Update particle position
      particleOffset += speed * deltaTime;
      if (particleOffset > approximateLength) {
        particleOffset = 0;
      }
      
      const t = particleOffset / approximateLength;
      const point = getBezierPoint(t);
      
      // Clear canvas
      ctx.clearRect(0, 0, width, height);
      
      // Draw base path
      ctx.strokeStyle = stroke;
      ctx.lineWidth = strokeWidth;
      ctx.lineCap = 'round';
      ctx.lineJoin = 'round';
      
      ctx.beginPath();
      ctx.moveTo(localSourceX, localSourceY);
      ctx.bezierCurveTo(
        localSourceX, localSourceY + controlPointOffset,
        localTargetX, localTargetY - controlPointOffset,
        localTargetX, localTargetY
      );
      ctx.stroke();
      
      // Draw particle
      // Outer glow
      ctx.fillStyle = stroke + '40';
      ctx.beginPath();
      ctx.arc(point.x, point.y, 12, 0, Math.PI * 2);
      ctx.fill();
      
      // Main particle
      ctx.fillStyle = stroke;
      ctx.beginPath();
      ctx.arc(point.x, point.y, 6, 0, Math.PI * 2);
      ctx.fill();
      
      // Inner core
      ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      ctx.beginPath();
      ctx.arc(point.x, point.y, 3, 0, Math.PI * 2);
      ctx.fill();
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animationRef.current = requestAnimationFrame(animate);
    }, 10); // Small delay to ensure canvas is mounted

    return () => {
      clearTimeout(timeoutId);
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [isActive, edgeAnimationsEnabled, sourceX, sourceY, targetX, targetY, stroke, strokeWidth, duration]);

  // Calculate canvas position
  const padding = 50;
  const minX = Math.min(sourceX, targetX) - padding;
  const minY = Math.min(sourceY, targetY) - padding;
  const deltaY = targetY - sourceY;
  const controlPointOffset = Math.abs(deltaY) * 0.5;

  console.log('SimpleMovingEdge render:', { isActive, edgeAnimationsEnabled, sourceX, sourceY, targetX, targetY });

  return (
    <>
      {/* Base SVG path for fallback */}
      <g className={className}>
        <path
          d={`M ${sourceX} ${sourceY} C ${sourceX} ${sourceY + controlPointOffset} ${targetX} ${targetY - controlPointOffset} ${targetX} ${targetY}`}
          fill="none"
          stroke={stroke}
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeLinejoin="round"
          style={{
            borderRadius: '8px',
          }}
          className="transition-none"
        />
      </g>

      {/* Canvas for animation */}
      {isActive && edgeAnimationsEnabled ? (
        <div
          style={{
            position: 'fixed',
            left: `${minX}px`,
            top: `${minY}px`,
            pointerEvents: 'none',
            zIndex: 1000,
            border: '2px solid red', // Debug border to see if canvas container is visible
            width: '100px',
            height: '100px'
          }}
        >
          <canvas
            ref={canvasRef}
            style={{
              display: 'block',
              backgroundColor: 'rgba(255, 0, 0, 0.1)' // Debug background to see canvas
            }}
          />
          <div style={{ color: 'red', fontSize: '12px' }}>CANVAS HERE</div>
        </div>
      ) : (
        <div style={{ position: 'fixed', top: '10px', left: '10px', color: 'red', zIndex: 9999 }}>
          NO CANVAS: isActive={String(isActive)}, edgeAnimationsEnabled={String(edgeAnimationsEnabled)}
        </div>
      )}
    </>
  );
});
