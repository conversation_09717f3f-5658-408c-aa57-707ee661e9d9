"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Edit3, Loader2, X } from "lucide-react"

export function EditNodeModal({
  isOpen,
  onOpenChange,
  currentPosition = "",
  currentReward = "",
  onConfirm,
  isLoading = false
}) {
  const [position, setPosition] = useState(currentPosition)
  const [reward, setReward] = useState(currentReward)

  // Update local state when props change
  useEffect(() => {
    setPosition(currentPosition)
    setReward(currentReward)
  }, [currentPosition, currentReward, isOpen])

  const handleConfirm = () => {
    // If no changes made, just close modal
    if (position === currentPosition && reward === currentReward) {
      onOpenChange(false)
      return
    }

    // Use current values if fields are empty or invalid
    const finalPosition = position.trim() || currentPosition
    const finalReward = reward === "" ? currentReward : parseFloat(reward)

    onConfirm(finalPosition, finalReward)
  }

  const handleCancel = () => {
    // Reset to original values
    setPosition(currentPosition)
    setReward(currentReward)
    onOpenChange(false)
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleConfirm()
    } else if (e.key === "Escape") {
      handleCancel()
    }
  }

  // Always allow saving - if fields are empty, use current values
  const isValid = true

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md mx-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-blue-600 dark:text-blue-400">
            <Edit3 className="w-5 h-5" />
            ნოუდის რედაქტირება
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Position input */}
          <div className="space-y-2">
            <Label htmlFor="position" className="text-sm font-medium">
              პოზიცია *
            </Label>
            <Input
              id="position"
              value={position}
              onChange={(e) => setPosition(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={currentPosition}
              className="w-full"
              disabled={isLoading}
            />
          </div>

          {/* Reward input */}
          <div className="space-y-2">
            <Label htmlFor="reward" className="text-sm font-medium">
              რიუორდი (%) *
            </Label>
            <Input
              id="reward"
              type="number"
              min="0"
              step="0.1"
              value={reward}
              onChange={(e) => setReward(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="შეიყვანეთ რიუორდი"
              className="w-full"
              disabled={isLoading}
            />
          </div>

          {/* Info message */}
          <div className="text-sm text-muted-foreground">
            თუ ველებს არ შეცვლით, ძველი ვალიუები შენარჩუნდება
          </div>

          {/* Action buttons */}
          <div className="flex gap-3 pt-2">
            <Button
              variant="outline"
              onClick={handleCancel}
              className="flex-1"
              disabled={isLoading}
            >
              გაუქმება
            </Button>
            <Button
              onClick={handleConfirm}
              className="flex-1"
              disabled={isLoading || !isValid}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  ინახება...
                </>
              ) : (
                <>
                  <Edit3 className="w-4 h-4 mr-2" />
                  შენახვა
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
