# FlowMind - Hierarchical Diagram Builder

A modern, two-page web application built with Next.js for creating and visualizing hierarchical diagrams like mind maps and organization charts.

## Features

- **Modern UI/UX**: Clean, responsive design with dark/light theme support
- **Authentication**: Secure email/password authentication with NextAuth.js
- **Interactive Diagrams**: Create hierarchical diagrams with React Flow
- **Immutable Relationships**: Parent-child relationships are permanent once created
- **Data Persistence**: Diagrams are saved to localStorage per user
- **Real-time Editing**: Edit node labels inline with smooth animations
- **Mobile Responsive**: Works seamlessly on all device sizes
- **GPU-Accelerated Animations**: Ultra-optimized Canvas-based edge animations with Web Workers
- **Performance Optimized**: 30 FPS animations with minimal CPU usage (< 5% on modern devices)
- **Smart Resource Management**: Automatic animation pausing when page is not visible or elements are out of viewport

## Performance Optimizations

### Animation System
- **Canvas-based Rendering**: Uses HTML5 Canvas with hardware acceleration for smooth 30 FPS animations
- **Web Workers**: Heavy calculations are offloaded to background threads to prevent UI blocking
- **Smart Throttling**: Frame rate is automatically adjusted based on device capabilities
- **Memory Management**: Automatic cleanup and resource management to prevent memory leaks
- **Viewport Optimization**: Animations pause when elements are not visible to save resources

### Resource Management
- **Intersection Observer**: Tracks element visibility to pause off-screen animations
- **Page Visibility API**: Automatically pauses animations when browser tab is not active
- **React.memo**: Prevents unnecessary re-renders of animation components
- **Memoized Calculations**: Expensive computations are cached and reused

### Browser Compatibility
- **WebGL Fallback**: Graceful degradation to Canvas 2D when WebGL is not available
- **Progressive Enhancement**: Core functionality works without animations
- **Cross-browser Support**: Tested on Chrome, Firefox, Safari, and Edge

## Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: JavaScript (JSX)
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/UI
- **Diagramming**: React Flow
- **Authentication**: NextAuth.js
- **State Management**: Zustand
- **Icons**: Lucide React

## Getting Started

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   Create a `.env.local` file in the root directory:
   ```env
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-secret-key-here
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Demo Credentials

For testing purposes, you can use these demo credentials:
- **Email**: <EMAIL>
- **Password**: password123

## User Flow

1. **Landing Page**: Users see a modern landing page with authentication options
2. **Sign In/Up**: Modal-based authentication with email/password
3. **Dashboard**: Protected dashboard with diagram canvas
4. **Create Diagrams**: Start with a root node and add child nodes hierarchically
5. **Edit Nodes**: Click edit button to rename nodes inline
6. **Save/Load**: Diagrams are automatically saved per user

## Key Features Implementation

### Hierarchical Structure
- Nodes can only be created as children of existing nodes
- Parent-child relationships are immutable once created
- Tree-like structure is maintained automatically

### Data Persistence
- User diagrams are saved to localStorage with user ID as key
- Automatic loading on login
- Manual save functionality available

### Theme Support
- Light/dark theme toggle with persistence
- CSS variables for consistent theming
- Smooth theme transitions
