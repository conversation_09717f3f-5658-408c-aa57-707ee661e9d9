"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>si<PERSON> } from "reactflow"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Check, X, Loader2, <PERSON>ert<PERSON><PERSON>gle, Hourglass, Trash2, Edit3 } from "lucide-react"
import useStore from "@/lib/store"
import { toast } from "sonner"
import { NodeTasksModal } from "./tasks-modal"
import { NodeApplyModal } from "./node-apply-modal"
import { DeleteNodeModal } from "./delete-node-modal"
import { EditNodeModal } from "./edit-node-modal"

export function CustomNode({ data, id }) {
  const [isEditing, setIsEditing] = useState(false)
  const [editValue, setEditValue] = useState(data.label)
  const [isAddingNode, setIsAddingNode] = useState(false)
  const [newNodePosition, setNewNodePosition] = useState("")
  const [newN<PERSON><PERSON><PERSON><PERSON>, setNewNodeReward] = useState("")
  const [isTasksModalOpen, setIsTasksModalOpen] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const [showApplyModal, setShowApplyModal] = useState(false)
  const [showDeleteModal, setShowDeleteModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isEditingNode, setIsEditingNode] = useState(false)
  const inputRef = useRef(null)
  const addNodePositionRef = useRef(null)
  const { updateNodeLabel, addNodeToBackend, deleteNodeFromBackend, editNodeFromBackend } = useStore()

  // Get status border color based on pointer_status_id
  const getStatusBorderColor = (statusId) => {
    switch (statusId) {
      case 1:
        return 'border-l-green-500'
      case 2:
        return 'border-l-yellow-500'
      case 3:
        return 'border-l-red-500'
      default:
        return 'border-l-gray-500'
    }
  }

  // Get text size class based on label length
  const getTextSizeClass = (label) => {
    if (!label) return 'text-sm'
    const length = label.length
    if (length <= 10) return 'text-base'
    if (length <= 15) return 'text-sm'
    if (length <= 20) return 'text-xs'
    return 'text-[10px]'
  }

  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [isEditing])

  useEffect(() => {
    if (isAddingNode && addNodePositionRef.current) {
      addNodePositionRef.current.focus()
    }
  }, [isAddingNode])

  const handleSave = async () => {
    if (editValue.trim() && editValue !== data.label) {
      try {
        await updateNodeLabel(id, editValue.trim())
        toast.success("ლეიბლი წარმატებით განახლდა!")
      } catch (error) {
        console.error('Failed to update label:', error)
        toast.error("ლეიბლის განახლება ვერ მოხერხდა")
        setEditValue(data.label) // Reset to original value
      }
    }
    setIsEditing(false)
  }

  const handleCancel = () => {
    setEditValue(data.label)
    setIsEditing(false)
  }

  const handleKeyDown = (e) => {
    if (e.key === "Enter") {
      handleSave()
    } else if (e.key === "Escape") {
      handleCancel()
    }
  }

  const handleAddNode = async () => {
    if (!newNodePosition.trim() || !newNodeReward.trim()) {
      toast.error("გთხოვთ შეავსოთ ყველა ველი")
      return
    }

    const reward = parseFloat(newNodeReward)
    if (isNaN(reward) || reward <= 0) {
      toast.error("რიუორდი უნდა იყოს დადებითი რიცხვი")
      return
    }

    setIsLoading(true)
    try {
      await addNodeToBackend(id, newNodePosition.trim(), reward)
      toast.success("ნოუდი წარმატებით დაემატა!")
      setIsAddingNode(false)
      setNewNodePosition("")
      setNewNodeReward("")
    } catch (error) {
      console.error('Failed to add node:', error)
      toast.error(error.message || "ნოუდის დამატება ვერ მოხერხდა")
    } finally {
      setIsLoading(false)
    }
  }

  const handleCancelAddNode = () => {
    setIsAddingNode(false)
    setNewNodePosition("")
    setNewNodeReward("")
  }

  const handleAddNodeKeyDown = (e) => {
    if (e.key === "Enter") {
      handleAddNode()
    } else if (e.key === "Escape") {
      handleCancelAddNode()
    }
  }

  // Check if user has any owned nodes in the entire hierarchy
  const userHasOwnedNodes = () => {
    if (!data.allNodes) return false
    return data.allNodes.some(node => node.is_mine)
  }

  // Check if user has any pending applications in the network
  const userHasPendingApplication = () => {
    if (!data.allNodes) return false
    return data.allNodes.some(node => node.contract_status_id === 1)
  }

  // Check if any node in the network has contracts_exists = true
  const networkHasContractsExists = () => {
    if (!data.allNodes) return false
    return data.allNodes.some(node => node.contracts_exists === true)
  }

  // Check if this node can be applied to (red node and user has no owned nodes)
  const canApplyToNode = () => {
    // Must be a red node (pointer_status_id: 3)
    if (data.pointer_status_id !== 3) return false

    // Must not be user's own node
    if (data.is_mine) return false

    // Cannot apply if contract_status_id is 1 (application already sent)
    if (data.contract_status_id === 1) return false

    // Cannot apply if user already has a pending application anywhere in the network
    if (userHasPendingApplication()) return false

    // Cannot apply if any node in the network has contracts_exists = true
    // This means someone already applied and we can only apply to one position
    if (networkHasContractsExists()) return false

    // User must not have any owned nodes in the hierarchy
    return !userHasOwnedNodes()
  }

  // Check if this node can be clicked (user's own node or child of user's node)
  const isNodeClickable = () => {
    // Allow clicking on user's own nodes
    if (data && data.is_mine) {
      return true
    }

    // Check if this is a child of user's node
    if (data && data.parent_id && data.allNodes && Array.isArray(data.allNodes)) {
      try {
        const parentNode = data.allNodes.find(node => node && node.id === data.parent_id)
        if (parentNode && parentNode.is_mine) {
          return true
        }
      } catch (error) {
        console.error('Error checking parent node:', error)
        return false
      }
    }

    return false
  }

  // Handle Apply button click
  const handleApplyClick = (e) => {
    e.stopPropagation() // Prevent node click event
    setShowApplyModal(true)
  }

  // Check if this node can be deleted (only my child nodes)
  const canDeleteNode = () => {
    // Must not be my own node
    if (data && data.is_mine) {
      return false
    }

    // Check if this is a child of user's node
    if (data && data.parent_id && data.allNodes && Array.isArray(data.allNodes)) {
      try {
        const parentNode = data.allNodes.find(node => node && node.id === data.parent_id)
        if (parentNode && parentNode.is_mine) {
          return true
        }
      } catch (error) {
        console.error('Error checking parent node for deletion:', error)
        return false
      }
    }

    return false
  }

  // Handle delete button click
  const handleDeleteClick = (e) => {
    e.stopPropagation() // Prevent node click event
    setShowDeleteModal(true)
  }

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    setIsDeleting(true)
    try {
      await deleteNodeFromBackend(id)
      toast.success("ნოუდი წარმატებით წაიშალა!")
      setShowDeleteModal(false)
    } catch (error) {
      console.error('Failed to delete node:', error)
      toast.error(error.message || "ნოუდის წაშლა ვერ მოხერხდა")
    } finally {
      setIsDeleting(false)
    }
  }

  // Handle edit button click
  const handleEditClick = (e) => {
    e.stopPropagation() // Prevent node click event
    setShowEditModal(true)
  }

  // Handle edit confirmation
  const handleEditConfirm = async (position, reward) => {
    setIsEditingNode(true)
    try {
      await editNodeFromBackend(id, position, reward)
      toast.success("ნოუდი წარმატებით განახლდა!")
      setShowEditModal(false)
    } catch (error) {
      console.error('Failed to edit node:', error)
      toast.error(error.message || "ნოუდის განახლება ვერ მოხერხდა")
    } finally {
      setIsEditingNode(false)
    }
  }

  // Handle node click to open tasks modal
  const handleNodeClick = () => {
    if (!isNodeClickable()) {
      return
    }

    setIsTasksModalOpen(true)
  }

  return (
    <div className="relative">
      {/* Input Handle (top) */}
      <Handle
        type="target"
        position={Position.Top}
        className="w-3 h-3 !bg-primary border-2 border-background"
      />
      
      {/* Node Content */}
      <div
        className={`border-2 border-l-4 rounded-lg shadow-lg w-[220px] h-[120px] transition-all duration-200 relative border-border group ${getStatusBorderColor(data.pointer_status_id || 1)} bg-card ${
          data.is_mine
            ? 'ring-2 ring-blue-600 ring-offset-2 ring-offset-background shadow-blue-300 dark:shadow-blue-900'
            : ''
        } ${
          data.contract_status_id === 1 && data.pointer_status_id === 3 && !userHasOwnedNodes()
            ? 'ring-2 ring-yellow-500 ring-offset-2 ring-offset-background shadow-yellow-300 dark:shadow-yellow-900 bg-yellow-50 dark:bg-yellow-900/20'
            : ''
        } ${
          canApplyToNode()
            ? 'cursor-pointer hover:shadow-xl hover:scale-105 backdrop-blur-sm bg-card/80'
            : isNodeClickable()
            ? 'cursor-pointer hover:shadow-xl hover:scale-105'
            : ''
        }`}
        onClick={handleNodeClick}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >

        {/* Circle positioning logic - all circles aligned vertically on the right */}
        {(() => {
          const circles = [];
          let currentPosition = -2; // Start from top (-top-2)

          // 1. Hourglass circle (highest priority) - New user with pending application
          if (data.contracts_exists === true) {
            circles.push(
              <div key="hourglass" className={`absolute -right-2 z-40`} style={{ top: `${currentPosition * 0.25}rem` }}>
                <div
                  className="bg-yellow-500 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold shadow-lg border-2 border-white dark:border-gray-800 animate-pulse cursor-help"
                  title="⏳ ახლად დარეგისტრირებულმა მომხმარებელმა ვაკანტური აპლიკაცია გამოაგზავნა"
                >
                  <Hourglass className="h-6 w-6 text-white animate-pulse" />
                </div>
              </div>
            );
            currentPosition += 12; // Move down for next circle
          }

          // 2. Warning circle (second priority) - Employee leaving
          if (data.contract_status_id === 5) {
            circles.push(
              <div key="warning" className={`absolute -right-2 z-30`} style={{ top: `${currentPosition * 0.25}rem` }}>
                <div
                  className="bg-red-500 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold shadow-lg border-2 border-white dark:border-gray-800 animate-pulse cursor-help"
                  title="⚠️ თანამშრომელი მალე დატოვებს პოზიციას! ყველა მიმდინარე ტასკის დასრულების შემდეგ სისტემა ავტომატურად გადავა ვაკანტურ რეჟიმში. ამ წამიდან ახალი ტასკების დამატება შეუძლებელია."
                >
                  <AlertTriangle className="h-6 w-6 text-white" />
                </div>
              </div>
            );
            currentPosition += 12; // Move down for next circle
          }

          // 3. Contracts count circle (third priority) - Vacant positions
          if (data.pointer_status_id === 3 && data.contracts_count > 0) {
            const contractsCount = data.contracts_count || 0;
            const formattedCount = contractsCount.toString();
            const textLength = formattedCount.length;

            let textSizeClass;
            if (textLength <= 1) {
              textSizeClass = 'text-[12px]';
            } else if (textLength <= 2) {
              textSizeClass = 'text-[11px]';
            } else if (textLength <= 3) {
              textSizeClass = 'text-[10px]';
            } else {
              textSizeClass = 'text-[9px]';
            }

            circles.push(
              <div key="contracts" className={`absolute -right-2 z-20`} style={{ top: `${currentPosition * 0.25}rem` }}>
                <div
                  className={`bg-red-500 text-white rounded-full w-10 h-10 flex items-center justify-center ${textSizeClass} font-bold shadow-lg border-2 border-white dark:border-gray-800`}
                >
                  {formattedCount}
                </div>
              </div>
            );
            currentPosition += 12; // Move down for next circle
          }

          // 4. Reward circle (fourth priority) - Show for all nodes with reward
          if (data.reward) {
            const formattedReward = `${parseFloat(data.reward).toString()}%`;
            const textLength = formattedReward.length;

            let textSizeClass;
            if (textLength <= 3) {
              textSizeClass = 'text-[12px]';
            } else if (textLength <= 4) {
              textSizeClass = 'text-[11px]';
            } else if (textLength <= 5) {
              textSizeClass = 'text-[10px]';
            } else {
              textSizeClass = 'text-[9px]';
            }

            circles.push(
              <div key="reward" className={`absolute -right-2 z-10`} style={{ top: `${currentPosition * 0.25}rem` }}>
                <div
                  className={`bg-violet-500 text-white rounded-full w-10 h-10 flex items-center justify-center ${textSizeClass} font-bold shadow-lg border-2 border-white dark:border-gray-800`}
                >
                  {formattedReward}
                </div>
              </div>
            );
          }

          return circles;
        })()}



        <div className="p-3 h-full flex flex-col justify-center">
          {isEditing ? (
            <div className="space-y-2">
              <Input
                ref={inputRef}
                value={editValue}
                onChange={(e) => setEditValue(e.target.value)}
                onKeyDown={handleKeyDown}
                className="text-center font-medium"
              />
              <div className="flex justify-center space-x-2">
                <Button size="sm" onClick={handleSave}>
                  <Check className="h-3 w-3" />
                </Button>
                <Button size="sm" variant="outline" onClick={handleCancel}>
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ) : isAddingNode ? (
            <div className="space-y-1">
              <Input
                ref={addNodePositionRef}
                value={newNodePosition}
                onChange={(e) => setNewNodePosition(e.target.value)}
                onKeyDown={handleAddNodeKeyDown}
                className="text-center font-medium text-xs"
                placeholder="პოზიცია"
              />
              <Input
                value={newNodeReward}
                onChange={(e) => setNewNodeReward(e.target.value)}
                onKeyDown={handleAddNodeKeyDown}
                className="text-center font-medium text-xs"
                placeholder="რიუორდი %"
                type="number"
                min="0"
                step="0.1"
              />
              <div className="flex justify-center space-x-1">
                <Button size="sm" onClick={handleAddNode} disabled={isLoading}>
                  {isLoading ? <Loader2 className="h-3 w-3 animate-spin" /> : <Check className="h-3 w-3" />}
                </Button>
                <Button size="sm" variant="outline" onClick={handleCancelAddNode} disabled={isLoading}>
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="text-center flex items-center justify-center h-full relative">
              <div className="w-full px-1">
                <h3 className={`${getTextSizeClass(data.label)} font-semibold text-center break-words leading-tight overflow-hidden`}>
                  {data.label}
                </h3>

                {/* Contract Status Loader - Show for vacant positions with pending application */}
                {data.contract_status_id === 1 && data.pointer_status_id === 3 && !userHasOwnedNodes() && (
                  <div className="absolute -top-2 -left-2 z-30">
                    <div className="bg-yellow-500 text-white rounded-full w-10 h-10 flex items-center justify-center font-bold shadow-lg border-2 border-white dark:border-gray-800 animate-pulse">
                      <Loader2 className="h-6 w-6 text-white animate-spin" />
                    </div>
                  </div>
                )}



                {/* Apply Indicator - minimal and elegant */}
                {canApplyToNode() && (
                  <div
                    className={`absolute inset-0 rounded-xl group transition duration-300 ease-in-out`}
                    onClick={handleApplyClick}
                  >
                    {/* Hover Overlay */}
                    <div className={`absolute inset-0 rounded-xl bg-gradient-to-br from-slate-100/60 to-white/20 dark:from-slate-800/30 dark:to-slate-700/20 opacity-0 group-hover:opacity-100 transition duration-300 ease-in-out pointer-events-none`} />

                    {/* Floating Button */}
                    <div className={`absolute bottom-3 right-3 transform transition duration-300 ease-in-out group-hover:scale-100 group-hover:opacity-100 scale-95 opacity-0`}>
                      <div className="bg-indigo-600 hover:bg-indigo-700 text-white text-sm font-semibold px-4 py-2 rounded-full shadow-md shadow-indigo-300/40 dark:shadow-none transition">
                        მოთხოვნის გაგზავნა
                      </div>
                    </div>
                  </div>
                )}

                {/* Delete Button - only for my child nodes, shown on hover */}
                {canDeleteNode() && (
                  <div className="absolute -top-2 -left-2 z-30 opacity-0 group-hover:opacity-100 transition-all duration-300 ease-in-out transform scale-75 group-hover:scale-100">
                    <button
                      onClick={handleDeleteClick}
                      className="bg-red-500 hover:bg-red-600 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg border-2 border-white dark:border-gray-800 transition-all duration-200 hover:scale-110"
                      title="ნოუდის წაშლა"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  </div>
                )}

                {/* Edit Button - only for my child nodes, shown on hover, below delete button */}
                {canDeleteNode() && (
                  <div className="absolute top-6 -left-2 z-30 opacity-0 group-hover:opacity-100 transition-all duration-300 ease-in-out transform scale-75 group-hover:scale-100">
                    <button
                      onClick={handleEditClick}
                      className="bg-blue-500 hover:bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center shadow-lg border-2 border-white dark:border-gray-800 transition-all duration-200 hover:scale-110"
                      title="ნოუდის რედაქტირება"
                    >
                      <Edit3 className="w-4 h-4" />
                    </button>
                  </div>
                )}

              </div>
            </div>
          )}
        </div>
      </div>

      {/* Output Handle (bottom) */}
      <Handle
        type="source"
        position={Position.Bottom}
        className="w-3 h-3 !bg-primary border-2 border-background"
      />

      {/* Tasks Modal */}
      <NodeTasksModal
        isOpen={isTasksModalOpen}
        onOpenChange={setIsTasksModalOpen}
        nodeId={id}
        nodeLabel={data.label}
        isUserOwnNode={data.is_mine}
        nodeStatusId={data.pointer_status_id}
        nodeUser={data.node_user}
        nodeContractStatusId={data.contract_status_id}
        nodeOwnerInfo={data}
        canEditNode={canDeleteNode()}
        nodePosition={data.position || ""}
        nodeReward={data.reward || ""}
        onNodeEdit={handleEditConfirm}
        onNodeDelete={handleDeleteConfirm}
      />

      {/* Apply Modal */}
      <NodeApplyModal
        isOpen={showApplyModal}
        onOpenChange={setShowApplyModal}
        nodeId={id}
        nodeLabel={data.label}
        onSuccess={() => {
          // Reload network data after successful application
          window.dispatchEvent(new CustomEvent('networkDataUpdated'))
        }}
      />

      {/* Delete Node Modal */}
      <DeleteNodeModal
        isOpen={showDeleteModal}
        onOpenChange={setShowDeleteModal}
        nodeLabel={data.label}
        onConfirm={handleDeleteConfirm}
        isLoading={isDeleting}
      />

      {/* Edit Node Modal */}
      <EditNodeModal
        isOpen={showEditModal}
        onOpenChange={setShowEditModal}
        currentPosition={data.position || ""}
        currentReward={data.reward || ""}
        onConfirm={handleEditConfirm}
        isLoading={isEditingNode}
      />

    </div>
  )
}
