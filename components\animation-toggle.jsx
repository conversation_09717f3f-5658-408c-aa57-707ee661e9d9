"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Zap, ZapOff } from "lucide-react"
import useStore from "@/lib/store"
import { toast } from "sonner"

export function AnimationToggle() {
  const { edgeAnimationsEnabled, setEdgeAnimationsEnabled } = useStore()

  const handleToggle = () => {
    const newState = !edgeAnimationsEnabled
    setEdgeAnimationsEnabled(newState)

    if (newState) {
      toast.success("Edge animations enabled")
    } else {
      toast.success("Edge animations disabled - Power save mode")
    }
  }

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleToggle}
      className="flex items-center space-x-1 sm:space-x-2"
      title={edgeAnimationsEnabled ? "Disable animations (Power save)" : "Enable animations"}
    >
      {edgeAnimationsEnabled ? (
        <Zap className="h-3 w-3 sm:h-4 sm:w-4 text-yellow-500" />
      ) : (
        <ZapOff className="h-3 w-3 sm:h-4 sm:w-4 text-gray-500" />
      )}
      <span className="hidden sm:inline">
        {edgeAnimationsEnabled ? "Animations" : "Power Save"}
      </span>
    </Button>
  )
}
