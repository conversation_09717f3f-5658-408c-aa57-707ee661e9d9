<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Applicant Popup Visual</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateX(300px) scale(0.8);
            }
            to {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
        }
        
        @keyframes slideOut {
            from {
                opacity: 1;
                transform: translateX(0) scale(1);
            }
            to {
                opacity: 0;
                transform: translateX(300px) scale(0.8);
            }
        }
        
        .slide-in {
            animation: slideIn 0.3s ease-out forwards;
        }
        
        .slide-out {
            animation: slideOut 0.3s ease-out forwards;
        }
        
        @keyframes progressBar {
            from { width: 100%; }
            to { width: 0%; }
        }
        
        .progress-animation {
            animation: progressBar 4s linear forwards;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">🔔 Applicant Popup Visual Test</h1>
        
        <div class="bg-white rounded-lg p-6 shadow-lg mb-8">
            <h2 class="text-xl font-semibold mb-4">Test Controls</h2>
            <div class="space-y-4">
                <div>
                    <label class="block text-sm font-medium mb-2">Position:</label>
                    <input type="text" id="position" value="Frontend Developer" class="w-full p-2 border rounded">
                </div>
                <div class="grid grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">Rating:</label>
                        <input type="number" id="rating" value="8" min="1" max="10" class="w-full p-2 border rounded">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Leave Count:</label>
                        <input type="number" id="leaveCount" value="2" min="0" class="w-full p-2 border rounded">
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-2">Reject Count:</label>
                        <input type="number" id="rejectCount" value="1" min="0" class="w-full p-2 border rounded">
                    </div>
                </div>
                <button onclick="showPopup()" class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                    Show Popup
                </button>
            </div>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Instructions</h2>
            <ol class="list-decimal list-inside space-y-2 text-gray-700">
                <li>Fill in the test data above</li>
                <li>Click "Show Popup" to see the popup animation</li>
                <li>The popup will appear in the top-right corner</li>
                <li>It will automatically close after 4 seconds</li>
                <li>You can manually close it by clicking the X button</li>
            </ol>
        </div>
    </div>

    <!-- Popup Container -->
    <div id="popup" class="fixed top-4 right-4 z-50 w-80 bg-white rounded-lg shadow-2xl border border-gray-200 overflow-hidden hidden">
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-500 px-4 py-3 text-white relative">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                    <span class="font-semibold">ახალი აპლიკანტი!</span>
                </div>
                <button onclick="hidePopup()" class="text-white/80 hover:text-white transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
            
            <!-- Progress bar -->
            <div id="progressBar" class="absolute bottom-0 left-0 h-1 bg-white/30"></div>
        </div>

        <!-- Content -->
        <div class="p-4 space-y-3">
            <!-- Position -->
            <div class="text-center">
                <p id="positionText" class="text-lg font-medium text-gray-800">
                    📋 Frontend Developer
                </p>
                <p class="text-sm text-gray-600">
                    პოზიციაზე მოთხოვნა გამოიგზავნა
                </p>
            </div>

            <!-- Quick stats -->
            <div class="grid grid-cols-3 gap-2 text-xs">
                <div class="bg-yellow-50 rounded p-2 text-center">
                    <div class="flex items-center justify-center gap-1 text-yellow-600">
                        <svg class="w-3 h-3 fill-current" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span id="ratingText" class="font-bold">8/10</span>
                    </div>
                    <div class="text-yellow-700 mt-1">რეიტინგი</div>
                </div>
                
                <div class="bg-orange-50 rounded p-2 text-center">
                    <div id="leaveText" class="font-bold text-orange-600">2</div>
                    <div class="text-orange-700 mt-1">გასვლა</div>
                </div>
                
                <div class="bg-red-50 rounded p-2 text-center">
                    <div id="rejectText" class="font-bold text-red-600">1</div>
                    <div class="text-red-700 mt-1">უარყოფა</div>
                </div>
            </div>

            <!-- Timer -->
            <div class="text-center">
                <span id="timerText" class="text-xs text-gray-500">
                    დაიხურება 4 წამში
                </span>
            </div>
        </div>
    </div>

    <script>
        let popupTimer = null;
        let countdownTimer = null;
        let timeLeft = 4;

        function showPopup() {
            // Get values from inputs
            const position = document.getElementById('position').value;
            const rating = document.getElementById('rating').value;
            const leaveCount = document.getElementById('leaveCount').value;
            const rejectCount = document.getElementById('rejectCount').value;

            // Update popup content
            document.getElementById('positionText').textContent = `📋 ${position}`;
            document.getElementById('ratingText').textContent = `${rating}/10`;
            document.getElementById('leaveText').textContent = leaveCount;
            document.getElementById('rejectText').textContent = rejectCount;

            // Show popup
            const popup = document.getElementById('popup');
            popup.classList.remove('hidden');
            popup.classList.add('slide-in');

            // Start progress bar animation
            const progressBar = document.getElementById('progressBar');
            progressBar.classList.add('progress-animation');

            // Reset timer
            timeLeft = 4;
            updateTimer();

            // Start countdown
            countdownTimer = setInterval(() => {
                timeLeft--;
                updateTimer();
                if (timeLeft <= 0) {
                    hidePopup();
                }
            }, 1000);

            // Auto hide after 4 seconds
            popupTimer = setTimeout(() => {
                hidePopup();
            }, 4000);
        }

        function hidePopup() {
            const popup = document.getElementById('popup');
            popup.classList.remove('slide-in');
            popup.classList.add('slide-out');

            // Clear timers
            if (popupTimer) {
                clearTimeout(popupTimer);
                popupTimer = null;
            }
            if (countdownTimer) {
                clearInterval(countdownTimer);
                countdownTimer = null;
            }

            // Hide popup after animation
            setTimeout(() => {
                popup.classList.add('hidden');
                popup.classList.remove('slide-out');
                
                // Reset progress bar
                const progressBar = document.getElementById('progressBar');
                progressBar.classList.remove('progress-animation');
            }, 300);
        }

        function updateTimer() {
            document.getElementById('timerText').textContent = `დაიხურება ${timeLeft} წამში`;
        }
    </script>
</body>
</html>
