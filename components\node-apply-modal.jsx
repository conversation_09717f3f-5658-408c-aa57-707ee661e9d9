"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { AlertTriangle, Loader2 } from "lucide-react"
import { toast } from "sonner"
import { api } from "@/lib/api"

export function NodeApplyModal({ isOpen, onOpenChange, nodeId, nodeLabel, onSuccess }) {
  const [isLoading, setIsLoading] = useState(false)

  const handleApply = async () => {
    setIsLoading(true)
    try {
      const response = await api.network.applyToNode(nodeId)
      
      if (response.success) {
        toast.success("თქვენი მოთხოვნა წარმატებით გაიგზავნა!")
        onOpenChange(false)
        if (onSuccess) {
          onSuccess()
        }
      } else {
        toast.error(response.message || "მოთხოვნის გაგზავნა ვერ მოხერხდა")
      }
    } catch (error) {
      console.error('Failed to apply to node:', error)
      toast.error(error.message || "მოთხოვნის გაგზავნა ვერ მოხერხდა")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-orange-600">
            <AlertTriangle className="h-5 w-5" />
            ნოუდზე მუშაობის მოთხოვნა
          </DialogTitle>
          <DialogDescription>
            ნამდვილად გსურთ მუშაობა ნოუდზე: "{nodeLabel}"?
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 space-y-2">
            <div className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              ⚠️ მნიშვნელოვანი გაფრთხილება:
            </div>
            <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1 list-disc list-inside">
              <li>თქვენ იღებთ ვალდებულებას მაქსიმალურად იზრუნოთ ამ ნოუდზე</li>
              <li>უნდა შეასრულოთ ყველა დავალება ხარისხიანად და დროულად</li>
              <li>ნოუდი არის კრიპტოვალუტა - მისი ღირებულება თქვენზეა დამოკიდებული</li>
              <li>რამდენადაც მაქსიმალურს გააკეთებთ, იგივენაირად მაქსიმალურს მიიღებთ</li>
            </ul>
          </div>

          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 space-y-2">
            <div className="text-sm font-medium text-red-800 dark:text-red-200">
              🚨 დისკვალიფიკაციის რისკი:
            </div>
            <ul className="text-sm text-red-700 dark:text-red-300 space-y-1 list-disc list-inside">
              <li>თუ დავალებებს არ შეასრულებთ ან შეასრულებთ უხარისხოდ</li>
              <li>სისტემის მიერ ავტომატურად დისკვალიფიცირდებით</li>
              <li>ადმინისტრაციას ასევე შეუძლია თქვენი გათხოვება</li>
            </ul>
          </div>

          <div className="text-sm font-medium text-center text-muted-foreground">
            ნამდვილად ხართ მზად ამ პასუხისმგებლობისთვის?
          </div>
        </div>

        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            className="flex-1"
            disabled={isLoading}
          >
            გაუქმება
          </Button>
          <Button
            onClick={handleApply}
            className="flex-1 bg-green-600 hover:bg-green-700"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                იგზავნება...
              </>
            ) : (
              "დიახ, ვაპლიკაციას"
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
