// Test script to verify parent_id error fixes
// This script tests the scenarios that could cause the parent_id error

console.log('Testing parent_id error fixes...')

// Test 1: Test null object handling in array filtering
function testNullObjectFiltering() {
  console.log('\n1. Testing null object filtering...')
  
  const testArray = [
    { id: 1, parent_id: null, name: 'Root' },
    null, // This should be filtered out
    { id: 2, parent_id: 1, name: 'Child' },
    undefined, // This should be filtered out
    { id: 3, parent_id: 2, name: 'Grandchild' },
    { parent_id: 1 }, // Missing id, should be filtered out
  ]
  
  // Test the filtering logic from tasks-modal.jsx
  const validTasks = testArray.filter(task => task && typeof task === 'object' && task.id)
  console.log('Original array length:', testArray.length)
  console.log('Filtered array length:', validTasks.length)
  console.log('Valid tasks:', validTasks)
  
  if (validTasks.length === 3) {
    console.log('✅ Null object filtering works correctly')
  } else {
    console.log('❌ Null object filtering failed')
  }
}

// Test 2: Test parent-child relationship building with null objects
function testParentChildMapping() {
  console.log('\n2. Testing parent-child relationship building...')
  
  const testNodes = [
    { id: 1, parent_id: null, name: 'Root' },
    null, // This should be skipped
    { id: 2, parent_id: 1, name: 'Child' },
    { id: 3, parent_id: 1, name: 'Child2' },
    undefined, // This should be skipped
    { id: 4, parent_id: 2, name: 'Grandchild' },
  ]
  
  // Test the logic from diagram-canvas.jsx
  const childrenMap = new Map()
  testNodes.forEach(node => {
    if (node && typeof node === 'object' && node.hasOwnProperty('parent_id') && node.parent_id !== null) {
      if (!childrenMap.has(node.parent_id)) {
        childrenMap.set(node.parent_id, [])
      }
      childrenMap.get(node.parent_id).push(node)
    }
  })
  
  console.log('Children map:', childrenMap)
  
  if (childrenMap.has(1) && childrenMap.get(1).length === 2) {
    console.log('✅ Parent-child mapping works correctly')
  } else {
    console.log('❌ Parent-child mapping failed')
  }
}

// Test 3: Test root node filtering
function testRootNodeFiltering() {
  console.log('\n3. Testing root node filtering...')
  
  const testNodes = [
    { id: 1, parent_id: null, name: 'Root1' },
    null, // This should be skipped
    { id: 2, parent_id: 1, name: 'Child' },
    { id: 3, parent_id: null, name: 'Root2' },
    undefined, // This should be skipped
  ]
  
  // Test the logic from diagram-canvas.jsx
  const rootNodes = testNodes.filter(node => node && typeof node === 'object' && node.parent_id === null)
  
  console.log('Root nodes:', rootNodes)
  
  if (rootNodes.length === 2) {
    console.log('✅ Root node filtering works correctly')
  } else {
    console.log('❌ Root node filtering failed')
  }
}

// Test 4: Test node clickability check
function testNodeClickability() {
  console.log('\n4. Testing node clickability check...')
  
  const testAllNodes = [
    { id: 1, parent_id: null, is_mine: true, name: 'MyRoot' },
    { id: 2, parent_id: 1, is_mine: false, name: 'Child' },
    { id: 3, parent_id: 2, is_mine: false, name: 'Grandchild' },
  ]
  
  // Test the logic from custom-node.jsx
  function isNodeClickable(data) {
    if (data && data.is_mine) {
      return true
    }
    
    if (data && data.parent_id && data.allNodes && Array.isArray(data.allNodes)) {
      try {
        const parentNode = data.allNodes.find(node => node && node.id === data.parent_id)
        if (parentNode && parentNode.is_mine) {
          return true
        }
      } catch (error) {
        console.error('Error checking parent node:', error)
        return false
      }
    }
    
    return false
  }
  
  // Test cases
  const testCases = [
    { data: { id: 1, is_mine: true, allNodes: testAllNodes }, expected: true, name: 'Own node' },
    { data: { id: 2, parent_id: 1, is_mine: false, allNodes: testAllNodes }, expected: true, name: 'Child of own node' },
    { data: { id: 3, parent_id: 2, is_mine: false, allNodes: testAllNodes }, expected: false, name: 'Grandchild of own node' },
    { data: null, expected: false, name: 'Null data' },
    { data: { id: 4, parent_id: 999, is_mine: false, allNodes: testAllNodes }, expected: false, name: 'Non-existent parent' },
  ]
  
  let passed = 0
  testCases.forEach(testCase => {
    const result = isNodeClickable(testCase.data)
    if (result === testCase.expected) {
      console.log(`✅ ${testCase.name}: ${result}`)
      passed++
    } else {
      console.log(`❌ ${testCase.name}: expected ${testCase.expected}, got ${result}`)
    }
  })
  
  if (passed === testCases.length) {
    console.log('✅ All node clickability tests passed')
  } else {
    console.log(`❌ ${testCases.length - passed} node clickability tests failed`)
  }
}

// Run all tests
testNullObjectFiltering()
testParentChildMapping()
testRootNodeFiltering()
testNodeClickability()

console.log('\n🎉 Parent ID error fix testing completed!')
