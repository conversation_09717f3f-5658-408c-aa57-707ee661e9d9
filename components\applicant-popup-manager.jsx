"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-context"
import { ApplicantPopup } from "./applicant-popup"

export function ApplicantPopupManager() {
  const { user } = useAuth()
  const [popupQueue, setPopupQueue] = useState([])
  const [currentPopup, setCurrentPopup] = useState(null)
  const [isVisible, setIsVisible] = useState(false)

  // Initialize WebSocket connection for applicant notifications
  useEffect(() => {
    if (!user?.id) return

    const setupWebSocket = async () => {
      try {
        // Load Socket.IO if not already loaded
        if (!window.io) {
          const socketScript = document.createElement('script')
          socketScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/socket.io/2.5.0/socket.io.js'
          document.head.appendChild(socketScript)

          await new Promise((resolve, reject) => {
            socketScript.onload = resolve
            socketScript.onerror = reject
          })
        }

        // Load Laravel Echo if not already loaded
        if (!window.Echo) {
          const echoScript = document.createElement('script')
          echoScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/laravel-echo/1.15.3/echo.iife.js'
          document.head.appendChild(echoScript)

          await new Promise((resolve, reject) => {
            echoScript.onload = resolve
            echoScript.onerror = reject
          })
        }

        // Initialize Echo
        const echo = new window.Echo({
          broadcaster: 'socket.io',
          host: 'http://127.0.0.1:6001',
          forceNew: true,
          transports: ['websocket', 'polling']
        })

        // Listen to the applicant notification channel
        const applicantChannel = echo.channel(`laravel-database-new.applicant.for.user-${user.id}`)
        console.log('Listening to applicant popup channel:', `laravel-database-new.applicant.for.user-${user.id}`)

        // Listen for ApplicantNotification events
        applicantChannel
          .listen('.ApplicantNotification', (data) => {
            console.log('ApplicantNotification received for popup:', data)
            handleNewApplicant(data)
          })
          .listen('ApplicantNotification', (data) => {
            console.log('ApplicantNotification received for popup:', data)
            handleNewApplicant(data)
          })

        console.log('WebSocket connection established for applicant popups')

        // Trigger network data update to refresh applicant counts
        window.dispatchEvent(new CustomEvent('networkDataUpdated'))

        // Cleanup function
        return () => {
          if (echo) {
            echo.disconnect()
          }
        }

      } catch (error) {
        console.error('WebSocket setup failed for applicant popups:', error)
      }
    }

    const cleanup = setupWebSocket()
    return () => {
      if (cleanup && typeof cleanup.then === 'function') {
        cleanup.then(cleanupFn => cleanupFn && cleanupFn())
      }
    }
  }, [user?.id])

  // Handle new applicant notification
  const handleNewApplicant = (data) => {
    const applicantData = {
      node_position: data.node_position,
      user_id: data.user_id,
      user_rate: data.user_rate,
      user_leave_count: data.user_leave_count,
      user_reject_count: data.user_reject_count
    }

    console.log('Adding applicant to popup queue:', applicantData)
    
    setPopupQueue(prev => [...prev, applicantData])
  }

  // Process popup queue
  useEffect(() => {
    if (popupQueue.length > 0 && !isVisible) {
      const nextApplicant = popupQueue[0]
      console.log('Showing next applicant popup:', nextApplicant)
      
      setCurrentPopup(nextApplicant)
      setIsVisible(true)
      setPopupQueue(prev => prev.slice(1))
    }
  }, [popupQueue, isVisible])

  const handlePopupClose = () => {
    console.log('Closing applicant popup')
    setIsVisible(false)
    setCurrentPopup(null)
  }

  return (
    <ApplicantPopup
      applicantData={currentPopup}
      isVisible={isVisible}
      onClose={handlePopupClose}
    />
  )
}
