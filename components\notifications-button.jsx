"use client"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Bell, BellRing } from "lucide-react"
import useStore from "@/lib/store"
import { NotificationsDropdown } from "./notifications-dropdown"

export function NotificationsButton() {
  const [isOpen, setIsOpen] = useState(false)
  const buttonRef = useRef(null)
  const { unreadCount, loadNotifications, setNotifications } = useStore()

  useEffect(() => {
    // Load notifications on component mount
    loadNotifications()
  }, [loadNotifications])

  const handleToggle = () => {
    setIsOpen(prev => !prev)
  }

  return (
    <div className="relative">
      <Button
        ref={buttonRef}
        variant="outline"
        size="icon"
        onClick={handleToggle}
        className="relative hover:bg-accent transition-colors"
      >
        {unreadCount > 0 ? (
          <BellRing className="h-[1.2rem] w-[1.2rem] text-orange-500 animate-pulse" />
        ) : (
          <Bell className="h-[1.2rem] w-[1.2rem]" />
        )}
        
        {unreadCount > 0 && (
          <Badge 
            variant="destructive" 
            className="absolute -top-2 -right-2 h-5 w-5 flex items-center justify-center p-0 text-xs font-bold animate-bounce"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
        
        <span className="sr-only">Notifications</span>
      </Button>

      <NotificationsDropdown
        isOpen={isOpen}
        onOpenChange={setIsOpen}
        buttonRef={buttonRef}
      />
    </div>
  )
}
