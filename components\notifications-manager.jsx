"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/lib/auth-context"
import useStore from "@/lib/store"
import { NotificationModal } from "./notification-modal"
import { toast } from "sonner"

export function NotificationsManager() {
  const { user } = useAuth()
  const [currentNotification, setCurrentNotification] = useState(null)
  const [showModal, setShowModal] = useState(false)
  const [notificationQueue, setNotificationQueue] = useState([])
  const [hasProcessedInitialNotifications, setHasProcessedInitialNotifications] = useState(false)
  
  const {
    addNotification,
    markNotificationAsRead,
    notifications,
    notificationsLoaded
  } = useStore()

  // Initialize WebSocket connection
  useEffect(() => {
    if (!user?.id) return

    const setupWebSocket = async () => {
      try {
        // Load Socket.IO if not already loaded
        if (!window.io) {
          const socketScript = document.createElement('script')
          socketScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/socket.io/2.5.0/socket.io.js'
          document.head.appendChild(socketScript)

          await new Promise((resolve, reject) => {
            socketScript.onload = resolve
            socketScript.onerror = reject
          })
        }

        // Load Laravel Echo if not already loaded
        if (!window.Echo) {
          const echoScript = document.createElement('script')
          echoScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/laravel-echo/1.15.3/echo.iife.js'
          document.head.appendChild(echoScript)

          await new Promise((resolve, reject) => {
            echoScript.onload = resolve
            echoScript.onerror = reject
          })
        }

        // Initialize Echo
        const echo = new window.Echo({
          broadcaster: 'socket.io',
          host: 'http://127.0.0.1:6001',
          forceNew: true,
          transports: ['websocket', 'polling']
        })

        // Listen to the new notification channel
        const notificationChannel = echo.channel(`laravel-database-notification.user-${user.id}`)
        console.log('Listening to notification channel:', `laravel-database-notification.user-${user.id}`)

        // Listen for GeneralNotification events
        notificationChannel
          .listen('.GeneralNotification', (data) => {
            console.log('GeneralNotification received:', data)
            handleNewNotification(data.notification || data, true) // fromWebSocket = true
          })
          .listen('GeneralNotification', (data) => {
            console.log('GeneralNotification received:', data)
            handleNewNotification(data.notification || data, true) // fromWebSocket = true
          })

        // Keep the old channel for backward compatibility (JobAccepted)
        const userChannel = echo.channel(`laravel-database-user${user.id}`)
        console.log('Listening to user channel:', `laravel-database-user${user.id}`)
        userChannel
          .listen('.JobAccepted', (data) => {
            console.log('JobAccepted received:', data)
            // Convert JobAccepted to notification format
            const notification = {
              id: `job-accepted-${Date.now()}-${Math.random()}`, // Unique ID
              user_id: user.id,
              important: true,
              status_id: 1,
              rate: null,
              text: `გილოცავთ! თქვენ წარმატებით დაიკავეთ ადგილი ${data.position || 'პოზიციაზე'}`,
              read_at: null,
              created_at: new Date().toISOString()
            }
            handleNewNotification(notification, true) // fromWebSocket = true
          })
          .listen('JobAccepted', (data) => {
            console.log('JobAccepted received:', data)
            // Convert JobAccepted to notification format
            const notification = {
              id: `job-accepted-alt-${Date.now()}-${Math.random()}`, // Unique ID
              user_id: user.id,
              important: true,
              status_id: 1,
              rate: null,
              text: `გილოცავთ! თქვენ წარმატებით დაიკავეთ ადგილი ${data.position || 'პოზიციაზე'}`,
              read_at: null,
              created_at: new Date().toISOString()
            }
            handleNewNotification(notification, true) // fromWebSocket = true
          })

        console.log('WebSocket connection established for notifications')

        // Cleanup function
        return () => {
          if (echo) {
            echo.disconnect()
          }
        }

      } catch (error) {
        console.error('WebSocket setup failed:', error)
      }
    }

    const cleanup = setupWebSocket()
    return () => {
      if (cleanup && typeof cleanup.then === 'function') {
        cleanup.then(cleanupFn => cleanupFn && cleanupFn())
      }
    }
  }, [user?.id])

  // Handle new notification from WebSocket
  const handleNewNotification = (notification, fromWebSocket = false) => {
    console.log('New notification received:', notification, 'fromWebSocket:', fromWebSocket)

    // Add to store
    addNotification(notification)

    if (fromWebSocket) {
      // All WebSocket notifications show in modal regardless of important flag
      console.log('Adding WebSocket notification to modal queue:', notification)
      setNotificationQueue(prev => {
        // Check if notification already exists in queue to avoid duplicates
        const exists = prev.some(n => n.id === notification.id)
        if (exists) {
          console.log('Notification already in queue, skipping:', notification.id)
          return prev
        }
        return [...prev, notification]
      })
    } else {
      // From API endpoint - important notifications will be handled by useEffect
      // Only show toast for non-important notifications from API
      if (!notification.important || notification.read_at) {
        toast.info(notification.text, {
          duration: 5000,
          action: {
            label: "ნახვა",
            onClick: () => {
              // Could open notifications dropdown here
            }
          }
        })
      }
    }
  }

  // Process notification queue
  useEffect(() => {
    console.log('Notification queue updated:', notificationQueue, 'showModal:', showModal)
    if (notificationQueue.length > 0 && !showModal) {
      const nextNotification = notificationQueue[0]
      console.log('Showing next notification in modal:', nextNotification)
      setCurrentNotification(nextNotification)
      setShowModal(true)
      setNotificationQueue(prev => prev.slice(1))
    }
  }, [notificationQueue, showModal])

  // Load notifications on mount - removed duplicate call since NotificationsButton already loads them

  // Check for important unread notifications when notifications are loaded (only once)
  useEffect(() => {
    if (notificationsLoaded && notifications.length > 0 && !hasProcessedInitialNotifications) {
      // Only show modal for important=true AND read_at=null notifications from API
      const importantUnread = notifications.filter(n => n.important && !n.read_at)

      if (importantUnread.length > 0) {
        console.log('Found important unread notifications on initial load:', importantUnread)
        // Add all important notifications to the queue to show them one by one
        setNotificationQueue(prev => {
          // Filter out any duplicates that might already be in the queue
          const newNotifications = importantUnread.filter(newNotif =>
            !prev.some(existingNotif => existingNotif.id === newNotif.id)
          )
          console.log('Adding new notifications to queue:', newNotifications)
          return [...prev, ...newNotifications]
        })
      }

      // Mark that we've processed initial notifications
      setHasProcessedInitialNotifications(true)
    }
  }, [notificationsLoaded, notifications, hasProcessedInitialNotifications])

  const handleModalClose = () => {
    setShowModal(false)
    setCurrentNotification(null)
  }

  const handleMarkAsRead = (notificationId) => {
    markNotificationAsRead(notificationId)
  }

  return (
    <NotificationModal
      notification={currentNotification}
      isOpen={showModal}
      onOpenChange={handleModalClose}
      onMarkAsRead={handleMarkAsRead}
      queueLength={notificationQueue.length}
    />
  )
}
