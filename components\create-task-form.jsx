"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { api } from "@/lib/api"
import { toast } from "sonner"
import { Loader2, Calendar, Clock, ArrowLeft } from "lucide-react"
import { RichTextEditor, isHtmlEmpty } from "./rich-text-editor"

export function CreateTaskForm({ nodeId, nodeLabel, onCancel, onTaskCreated }) {
  const [isLoading, setIsLoading] = useState(false)
  const [taskData, setTaskData] = useState({
    task_description: "",
    type: 0, // 0 = განმეორებადი, 1 = ერთჯერადი
    weekday: 1, // 1 = ორშაბათი
    hour: 9,
    minute: 0,
    second: 0,
    end_date: ""
  })

  const weekdays = [
    { value: 0, label: "კვირა" },
    { value: 1, label: "ორშაბათი" },
    { value: 2, label: "სამშაბათი" },
    { value: 3, label: "ოთხშაბათი" },
    { value: 4, label: "ხუთშაბათი" },
    { value: 5, label: "პარასკევი" },
    { value: 6, label: "შაბათი" }
  ]

  const handleInputChange = (field, value) => {
    setTaskData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const validateForm = () => {
    if (isHtmlEmpty(taskData.task_description)) {
      toast.error("ტასკის აღწერა სავალდებულოა")
      return false
    }

    if (taskData.type === 0) {
      // განმეორებადი ტასკი - შევამოწმოთ weekday, hour, minute
      if (taskData.weekday < 0 || taskData.weekday > 6) {
        toast.error("გთხოვთ აირჩიოთ სწორი კვირის დღე")
        return false
      }
      if (taskData.hour < 0 || taskData.hour > 23) {
        toast.error("საათი უნდა იყოს 0-23 შორის")
        return false
      }
      if (taskData.minute < 0 || taskData.minute > 59) {
        toast.error("წუთი უნდა იყოს 0-59 შორის")
        return false
      }
    } else if (taskData.type === 1) {
      // ერთჯერადი ტასკი - შევამოწმოთ end_date
      if (!taskData.end_date) {
        toast.error("ერთჯერადი ტასკისთვის ვადა სავალდებულოა")
        return false
      }
      
      const endDate = new Date(taskData.end_date)
      const now = new Date()
      if (endDate <= now) {
        toast.error("ვადა უნდა იყოს მომავალში")
        return false
      }
    }

    return true
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsLoading(true)
    try {
      const submitData = {
        worker_id: parseInt(nodeId),
        task_description: taskData.task_description,
        type: taskData.type
      }

      if (taskData.type === 0) {
        // განმეორებადი ტასკი
        submitData.weekday = taskData.weekday
        submitData.hour = taskData.hour
        submitData.minute = taskData.minute
        submitData.second = taskData.second
      } else {
        // ერთჯერადი ტასკი
        submitData.end_date = taskData.end_date
      }

      const response = await api.tasks.createTask(submitData)
      
      if (response.message || response.success) {
        toast.success("ტასკი წარმატებით შეიქმნა!")
        onTaskCreated()
      } else {
        throw new Error(response.message || "ტასკის შექმნა ვერ მოხერხდა")
      }
    } catch (error) {
      console.error('Failed to create task:', error)
      toast.error(error.message || "ტასკის შექმნა ვერ მოხერხდა")
    } finally {
      setIsLoading(false)
    }
  }

  const formatDateTimeLocal = (date) => {
    const d = new Date(date)
    d.setMinutes(d.getMinutes() - d.getTimezoneOffset())
    return d.toISOString().slice(0, 16)
  }

  const getCurrentDateTime = () => {
    const now = new Date()
    now.setHours(now.getHours() + 1) // დავამატოთ 1 საათი მინიმუმ მომავალში იყოს
    return formatDateTimeLocal(now)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3 pb-4 border-b">
        <Button
          variant="outline"
          size="sm"
          onClick={onCancel}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="w-4 h-4" />
          უკან
        </Button>
        <div>
          <h3 className="text-lg font-semibold">ახალი ტასკის შექმნა</h3>
          <p className="text-sm text-muted-foreground">{nodeLabel} - ისთვის</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Task Description */}
        <div className="space-y-2">
          <Label htmlFor="task_description">ტასკის აღწერა *</Label>
          <RichTextEditor
            value={taskData.task_description}
            onChange={(value) => handleInputChange("task_description", value)}
            placeholder="მაგ: განაახლე სისტემა, დაამატე ახალი ფუნქციონალი..."
            disabled={isLoading}
            className="w-full"
          />
        </div>

        {/* Task Type */}
        <div className="space-y-2">
          <Label>ტასკის ტიპი *</Label>
          <div className="flex gap-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="type"
                value={0}
                checked={taskData.type === 0}
                onChange={(e) => handleInputChange("type", parseInt(e.target.value))}
                disabled={isLoading}
                className="w-4 h-4 text-primary"
              />
              <span className="text-sm">განმეორებადი (კვირაში)</span>
            </label>
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="radio"
                name="type"
                value={1}
                checked={taskData.type === 1}
                onChange={(e) => handleInputChange("type", parseInt(e.target.value))}
                disabled={isLoading}
                className="w-4 h-4 text-primary"
              />
              <span className="text-sm">ერთჯერადი</span>
            </label>
          </div>
        </div>

        {/* Conditional Fields based on Type */}
        {taskData.type === 0 ? (
          // განმეორებადი ტასკი
          <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              განმეორების პარამეტრები
            </h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="weekday">კვირის დღე *</Label>
                <select
                  id="weekday"
                  value={taskData.weekday}
                  onChange={(e) => handleInputChange("weekday", parseInt(e.target.value))}
                  disabled={isLoading}
                  className="w-full h-9 px-3 py-1 text-sm border border-input bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  {weekdays.map(day => (
                    <option key={day.value} value={day.value}>
                      {day.label}
                    </option>
                  ))}
                </select>
              </div>

              <div className="grid grid-cols-2 gap-2">
                <div className="space-y-2">
                  <Label htmlFor="hour">საათი *</Label>
                  <Input
                    id="hour"
                    type="number"
                    min="0"
                    max="23"
                    value={taskData.hour}
                    onChange={(e) => handleInputChange("hour", parseInt(e.target.value) || 0)}
                    disabled={isLoading}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="minute">წუთი *</Label>
                  <Input
                    id="minute"
                    type="number"
                    min="0"
                    max="59"
                    value={taskData.minute}
                    onChange={(e) => handleInputChange("minute", parseInt(e.target.value) || 0)}
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>
          </div>
        ) : (
          // ერთჯერადი ტასკი
          <div className="space-y-4 p-4 bg-muted/30 rounded-lg">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Clock className="w-4 h-4" />
              ვადის მითითება
            </h4>
            
            <div className="space-y-2">
              <Label htmlFor="end_date">ვადა *</Label>
              <Input
                id="end_date"
                type="datetime-local"
                value={taskData.end_date}
                onChange={(e) => handleInputChange("end_date", e.target.value)}
                min={getCurrentDateTime()}
                disabled={isLoading}
              />
            </div>
          </div>
        )}

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isLoading}
          >
            გაუქმება
          </Button>
          <Button
            type="submit"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                იქმნება...
              </>
            ) : (
              "ტასკის შექმნა"
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
