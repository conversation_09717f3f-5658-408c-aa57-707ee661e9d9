"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { motion, AnimatePresence } from "framer-motion"
import { 
  Info, 
  AlertTriangle, 
  AlertCircle, 
  Star, 
  Sparkles, 
  CheckCircle,
  X,
  Clock
} from "lucide-react"
import { api } from "@/lib/api"
import { formatDistanceToNow } from "date-fns"

export function NotificationModal({ notification, isOpen, onOpenChange, onMarkAsRead, queueLength = 0 }) {
  const [showAnimation, setShowAnimation] = useState(false)

  useEffect(() => {
    if (isOpen && notification) {
      setShowAnimation(true)
    }
  }, [isOpen, notification])

  const getStatusIcon = (statusId) => {
    switch (statusId) {
      case 1: return <Info className="w-8 h-8 text-blue-500" />
      case 2: return <AlertTriangle className="w-8 h-8 text-yellow-500" />
      case 3: return <AlertCircle className="w-8 h-8 text-red-500" />
      default: return <Info className="w-8 h-8 text-gray-500" />
    }
  }

  const getStatusColor = (statusId) => {
    switch (statusId) {
      case 1: return "from-blue-50 via-blue-50 to-blue-100 dark:from-blue-950 dark:via-blue-950 dark:to-blue-900"
      case 2: return "from-yellow-50 via-yellow-50 to-yellow-100 dark:from-yellow-950 dark:via-yellow-950 dark:to-yellow-900"
      case 3: return "from-red-50 via-red-50 to-red-100 dark:from-red-950 dark:via-red-950 dark:to-red-900"
      default: return "from-gray-50 via-gray-50 to-gray-100 dark:from-gray-950 dark:via-gray-950 dark:to-gray-900"
    }
  }

  const getStatusBorderColor = (statusId) => {
    switch (statusId) {
      case 1: return "border-blue-200 dark:border-blue-800"
      case 2: return "border-yellow-200 dark:border-yellow-800"
      case 3: return "border-red-200 dark:border-red-800"
      default: return "border-gray-200 dark:border-gray-800"
    }
  }

  const handleConfirm = () => {
    // Everything happens immediately and synchronously for instant response

    // 1. Mark as read locally first (synchronous)
    onMarkAsRead(notification.id)

    // 2. Start API call immediately in background (fire and forget)
    if (notification && !notification.read_at && notification.id) {
      const isWebSocketNotification = notification.id.toString().startsWith('job-accepted-')

      if (!isWebSocketNotification) {
        // Fire and forget API call - completely async, no blocking
        api.notifications.markAsRead(notification.id).catch(error => {
          console.error('Background API error:', error)
        })
      }
    }

    // 3. Start exit animation immediately
    setShowAnimation(false)
  }

  const handleAnimationComplete = () => {
    // Close modal when exit animation completes
    if (!showAnimation) {
      onOpenChange(false)
    }
  }

  const handleClose = () => {
    // Close modal immediately
    setShowAnimation(false)
    onOpenChange(false)

    // If notification is unread and user just closes without marking as read,
    // we could optionally mark it as read automatically or leave it unread
    // For now, let's leave it unread when user just closes
  }

  const formatTime = (dateString) => {
    try {
      return formatDistanceToNow(new Date(dateString), {
        addSuffix: true
      })
    } catch {
      return "ახლახან"
    }
  }

  if (!notification) return null

  return (
    <Dialog open={isOpen} onOpenChange={() => {}} modal={true}>
      <DialogContent className={`sm:max-w-[500px] w-[95vw] max-h-[90vh] overflow-hidden border-2 ${getStatusBorderColor(notification.status_id)} bg-gradient-to-br ${getStatusColor(notification.status_id)}`}>
        {/* Close button */}
        {/* <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="absolute right-4 top-4 z-50 rounded-full w-8 h-8 p-0 hover:bg-white/20"
        >
          <X className="w-4 h-4" />
        </Button> */}

        {/* Sparkle Animation */}
        <AnimatePresence>
          {showAnimation && notification.rate && (
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {[...Array(15)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute"
                  initial={{
                    x: Math.random() * 500,
                    y: Math.random() * 400,
                    scale: 0,
                    rotate: 0
                  }}
                  animate={{
                    scale: [0, 1, 0],
                    rotate: 360,
                    y: Math.random() * 400 + 100
                  }}
                  transition={{
                    duration: 2,
                    delay: Math.random() * 1,
                    ease: "easeOut"
                  }}
                >
                  <Sparkles className="w-4 h-4 text-yellow-400" />
                </motion.div>
              ))}
            </div>
          )}
        </AnimatePresence>

        <div className="relative z-10 p-6">
          <DialogHeader className="text-center space-y-4">
            <motion.div
              initial={{ scale: 0, rotate: -180 }}
              animate={{ scale: 1, rotate: 0 }}
              transition={{ duration: 0.8, type: "spring", bounce: 0.5 }}
              className="mx-auto"
            >
              <div className="w-16 h-16 rounded-full bg-white/80 dark:bg-gray-800/80 flex items-center justify-center shadow-lg">
                {getStatusIcon(notification.status_id)}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              <DialogTitle className="text-2xl font-bold text-gray-800 dark:text-gray-200">
                {notification.rate ? "🎉 შეფასება მიღებულია!" :
                 notification.important ? "📢 მნიშვნელოვანი შეტყობინება" :
                 "📢 ახალი ნოტიფიკაცია"}
              </DialogTitle>
              {queueLength > 0 && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
                  დარჩენილია კიდევ {queueLength} შეტყობინება
                </p>
              )}
            </motion.div>
          </DialogHeader>

          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: showAnimation ? 1 : 0, y: showAnimation ? 0 : -20 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.15, ease: "easeOut" }}
            onAnimationComplete={handleAnimationComplete}
            className="mt-6 space-y-6"
          >
            {/* Main notification content */}
            <div className="text-center space-y-4">
              <div className="bg-white/60 dark:bg-gray-800/60 rounded-lg p-4 backdrop-blur-sm">
                <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
                  {notification.text}
                </p>
              </div>
              
              {/* Rating display */}
              {notification.rate && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.8, type: "spring", bounce: 0.6 }}
                  className="flex items-center justify-center gap-2 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg p-3"
                >
                  <Star className="w-6 h-6 text-yellow-500 fill-current" />
                  <span className="text-2xl font-bold text-yellow-600 dark:text-yellow-400">
                    {notification.rate}/10
                  </span>
                  <Star className="w-6 h-6 text-yellow-500 fill-current" />
                </motion.div>
              )}



              {/* Time and status */}
              <div className="flex items-center justify-center gap-4 text-sm text-gray-600 dark:text-gray-400">
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  {formatTime(notification.created_at)}
                </div>
              </div>
            </div>

            {/* Action button */}
            <div className="flex justify-center pt-4">
              <Button
                onClick={handleConfirm}
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold px-8 py-3 rounded-full shadow-lg transform transition-all duration-200 hover:scale-105"
              >
                <CheckCircle className="w-5 h-5 mr-2" />
                {notification.read_at ? "დახურვა" :
                 queueLength > 0 ? "შემდეგი" : "გასაგებია"}
              </Button>
            </div>
          </motion.div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
