"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { api } from "@/lib/api"
import { toast } from "sonner"
import { <PERSON><PERSON><PERSON><PERSON>gle, Clock, Zap, User, Loader2 } from "lucide-react"

export function FireWorkerModal({ isOpen, onOpenChange, contractId, workerName, onSuccess }) {
  const [isLoading, setIsLoading] = useState(false)
  const [selectedOption, setSelectedOption] = useState(null)
  const [comment, setComment] = useState("")

  const handleFire = async (immediately) => {
    console.log('🔥 FireWorkerModal - contractId:', contractId, 'type:', typeof contractId)
    if (!contractId) {
      toast.error("მუშაკის ინფორმაცია არ არის ხელმისაწვდომი")
      return
    }

    if (!comment.trim()) {
      toast.error("კომენტარის დაწერა სავალდებულოა")
      return
    }

    setIsLoading(true)
    try {
      const response = await api.network.fireWorker(contractId, immediately, comment.trim())
      
      if (response.success) {
        const message = immediately 
          ? `${workerName} დაუყონებლივ გათავისუფლდა სამსახურიდან`
          : `${workerName}-ისთვის გაგზავნილია გათავისუფლების შეტყობინება`
        
        toast.success(message)
        onSuccess?.()
        onOpenChange(false)
      } else {
        toast.error(response.message || "გათავისუფლება ვერ მოხერხდა")
      }
    } catch (error) {
      console.error('Fire worker error:', error)
      toast.error("შეცდომა მოხდა გათავისუფლების დროს")
    } finally {
      setIsLoading(false)
    }
  }

  const handleClose = () => {
    if (!isLoading) {
      setSelectedOption(null)
      setComment("")
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[480px] w-full max-w-[95vw] max-h-[90vh] overflow-y-auto">
        <DialogHeader className="pb-2">
          <DialogTitle className="text-lg sm:text-xl font-bold text-center flex items-center justify-center gap-2 text-red-700 dark:text-red-400">
            <AlertTriangle className="w-5 h-5 sm:w-6 sm:h-6" />
            <span className="hidden sm:inline">თანამშრომლის გათავისუფლება</span>
            <span className="sm:hidden">გათავისუფლება</span>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4 sm:space-y-6 py-2 sm:py-4">
          {/* Warning Message */}
          <div className="p-3 sm:p-4 bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg">
            <p className="text-xs sm:text-sm text-amber-800 dark:text-amber-200">
              <strong>გაფრთხილება:</strong> ეს მოქმედება შეუქცევადია.
            </p>
          </div>

          {/* Options */}
          <div className="space-y-2 sm:space-y-3">
            <h3 className="text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100 mb-2 sm:mb-3">
              აირჩიეთ ტიპი:
            </h3>

            {/* Standard Dismissal */}
            <div
              className={`p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                selectedOption === 'standard'
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => setSelectedOption('standard')}
            >
              <div className="flex items-start gap-2 sm:gap-3">
                <Clock className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 dark:text-blue-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100 mb-1">
                    სტანდარტული
                  </h4>
                  <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 leading-tight">
                    ყველა ტასკის დასრულების შემდეგ ავტომატური გათავისუფლება
                  </p>
                </div>
              </div>
            </div>

            {/* Immediate Dismissal */}
            <div
              className={`p-3 sm:p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                selectedOption === 'immediate'
                  ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                  : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
              }`}
              onClick={() => setSelectedOption('immediate')}
            >
              <div className="flex items-start gap-2 sm:gap-3">
                <Zap className="w-4 h-4 sm:w-5 sm:h-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <h4 className="text-sm sm:text-base font-medium text-gray-900 dark:text-gray-100 mb-1">
                    დაუყონებლივი
                  </h4>
                  <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400 leading-tight">
                    მიმდინარე ტასკები რჩება, კონტრაქტი წყდება დაუყონებლივ
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Comment Field */}
          <div className="space-y-1 sm:space-y-2">
            <label className="text-xs sm:text-sm font-medium text-gray-900 dark:text-gray-100">
              კომენტარი <span className="text-red-500">*</span>
            </label>
            <textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="მიზეზი..."
              className="w-full p-2 sm:p-3 text-sm border border-gray-300 dark:border-gray-600 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:text-gray-100"
              rows={2}
              disabled={isLoading}
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 sm:gap-3 pt-2 sm:pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1 text-sm sm:text-base py-2 sm:py-3"
            >
              გაუქმება
            </Button>
            <Button
              onClick={() => handleFire(selectedOption === 'immediate')}
              disabled={!selectedOption || !comment.trim() || isLoading}
              className={`flex-1 text-sm sm:text-base py-2 sm:py-3 ${
                selectedOption === 'immediate'
                  ? 'bg-red-600 hover:bg-red-700 text-white'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2 animate-spin" />
                  <span className="hidden sm:inline">მუშავდება...</span>
                  <span className="sm:hidden">...</span>
                </>
              ) : (
                <>
                  <span className="hidden sm:inline">
                    {selectedOption === 'immediate' ? 'დაუყონებლივ გათავისუფლება' : 'გათავისუფლება'}
                  </span>
                  <span className="sm:hidden">
                    {selectedOption === 'immediate' ? 'დაუყონებლივ' : 'გათავისუფლება'}
                  </span>
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
