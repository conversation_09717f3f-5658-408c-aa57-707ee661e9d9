"use client"

import { useMemo } from "react"

// Status indicator component that shows a colored dot based on pointer_status_id
export function StatusIndicator({ statusId = 1, className = "" }) {
  const statusConfig = useMemo(() => {
    switch (statusId) {
      case 1:
        return {
          color: "bg-green-500",
          shadowColor: "shadow-green-500/50",
          label: "Active"
        }
      case 2:
        return {
          color: "bg-yellow-500", 
          shadowColor: "shadow-yellow-500/50",
          label: "Warning"
        }
      case 3:
        return {
          color: "bg-red-500",
          shadowColor: "shadow-red-500/50", 
          label: "Error"
        }
      default:
        return {
          color: "bg-gray-500",
          shadowColor: "shadow-gray-500/50",
          label: "Unknown"
        }
    }
  }, [statusId])

  return (
    <div 
      className={`
        relative inline-flex items-center justify-center
        w-3 h-3 rounded-full
        ${statusConfig.color}
        ${statusConfig.shadowColor}
        shadow-lg
        animate-pulse
        ${className}
      `}
      title={`Status: ${statusConfig.label}`}
    >
      {/* Inner glow effect */}
      <div 
        className={`
          absolute inset-0 rounded-full
          ${statusConfig.color}
          opacity-75
          animate-ping
        `}
      />
      
      {/* Core dot */}
      <div 
        className={`
          relative w-2 h-2 rounded-full
          ${statusConfig.color}
          border border-white/20
        `}
      />
    </div>
  )
}
