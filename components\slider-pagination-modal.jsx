"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { api } from "@/lib/api"
import { toast } from "sonner"
import { Loader2, Calendar, User, CheckCircle, Clock, AlertCircle, ChevronLeft, ChevronRight } from "lucide-react"

// Inline Badge component
const Badge = ({ variant = "default", className = "", children, ...props }) => {
  const baseClasses = "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
  
  const variantClasses = {
    default: "border-transparent bg-primary text-primary-foreground hover:bg-primary/80",
    secondary: "border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",
    destructive: "border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",
    outline: "text-foreground"
  }
  
  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${className}`} {...props}>
      {children}
    </div>
  )
}

export function NodeTasksModal({ isOpen, onOpenChange, nodeId, nodeLabel }) {
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalTasks, setTotalTasks] = useState(0)
  const [perPage] = useState(8) // 8 tasks per page - 2 rows of 4 on desktop, 1 row of 4 on mobile

  // Fetch tasks when modal opens or page changes
  useEffect(() => {
    if (isOpen && nodeId) {
      fetchTasks()
    }
  }, [isOpen, nodeId, currentPage])

  const fetchTasks = async () => {
    setLoading(true)
    try {
      const response = await api.tasks.getNodeTasks(nodeId, perPage, currentPage)
      console.log('Tasks API Response:', response) // Debug log

      // Check if response has data structure (direct API response format)
      if (response.data) {
        const paginationData = response.data
        console.log('Pagination Data:', paginationData) // Debug log
        console.log('Tasks array:', paginationData.data) // Debug log for tasks array
        console.log('Tasks count:', paginationData.data?.length) // Debug log for tasks count
        setTasks(paginationData.data || [])
        setCurrentPage(paginationData.current_page || 1)
        setTotalPages(paginationData.last_page || 1)
        setTotalTasks(paginationData.total || 0)
      } else {
        console.log('No data found in response:', response) // Debug log
        setTasks([])
        setTotalTasks(0)
      }
    } catch (error) {
      console.error('Failed to fetch tasks:', error)
      toast.error("ტასკების ჩატვირთვა ვერ მოხერხდა")
      setTasks([])
    } finally {
      setLoading(false)
    }
  }

  const handlePageChange = (page) => {
    if (page >= 1 && page <= totalPages && page !== currentPage) {
      setCurrentPage(page)
    }
  }

  const getStatusBadge = (statusId) => {
    switch (statusId) {
      case 1:
        return <Badge variant="default" className="bg-green-500 hover:bg-green-600"><CheckCircle className="w-3 h-3 mr-1" />დასრულებული</Badge>
      case 2:
        return <Badge variant="secondary" className="bg-blue-500 hover:bg-blue-600 text-white"><Clock className="w-3 h-3 mr-1" />მიმდინარე</Badge>
      case 0:
        return <Badge variant="outline" className="border-yellow-500 text-yellow-600"><AlertCircle className="w-3 h-3 mr-1" />მოლოდინში</Badge>
      default:
        return <Badge variant="outline">სტატუსი: {statusId}</Badge>
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A'
    try {
      return new Date(dateString).toLocaleDateString('ka-GE', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    } catch {
      return dateString
    }
  }

  const renderSliderPagination = () => {
    if (totalPages <= 1) return null

    return (
      <div className="mt-6 flex items-center justify-center gap-4">
        {/* Previous Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className="flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 hover:bg-primary hover:text-primary-foreground border-2"
        >
          <ChevronLeft className="w-4 h-4" />
          <span className="hidden sm:inline">წინა</span>
        </Button>

        {/* Page Indicator with Slider Effect */}
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            {/* Page dots */}
            <div className="flex items-center gap-1">
              {Array.from({ length: totalPages }, (_, index) => {
                const pageNum = index + 1
                const isActive = pageNum === currentPage
                return (
                  <button
                    key={pageNum}
                    onClick={() => handlePageChange(pageNum)}
                    className={`
                      w-3 h-3 rounded-full transition-all duration-300 ease-in-out cursor-pointer
                      ${isActive
                        ? 'bg-primary scale-125 shadow-lg ring-2 ring-primary/30'
                        : 'bg-muted-foreground/30 hover:bg-muted-foreground/60 hover:scale-110'
                      }
                    `}
                    aria-label={`გვერდი ${pageNum}`}
                  />
                )
              })}
            </div>

            {/* Page counter */}
            <div className="text-sm text-muted-foreground font-medium bg-muted/50 px-3 py-1 rounded-full">
              {currentPage} / {totalPages}
            </div>
          </div>
        </div>

        {/* Next Button */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className="flex items-center gap-2 px-4 py-2 rounded-full transition-all duration-200 hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100 hover:bg-primary hover:text-primary-foreground border-2"
        >
          <span className="hidden sm:inline">შემდეგი</span>
          <ChevronRight className="w-4 h-4" />
        </Button>
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] lg:max-w-[1100px] w-[900px] lg:w-[1100px] h-[600px] max-h-[85vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold text-center flex items-center justify-center gap-2">
            <User className="w-5 h-5" />
            {nodeLabel} - ტასკები
          </DialogTitle>
          <p className="text-sm text-muted-foreground text-center">
            სულ: {totalTasks} ტასკი
          </p>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">ტასკების ჩატვირთვა...</span>
            </div>
          ) : tasks.length === 0 ? (
            <div className="text-center py-12">
              <AlertCircle className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">ტასკები არ მოიძებნა</h3>
              <p className="text-muted-foreground">
                ამ ნოუდისთვის ჯერ არ არის მინიჭებული ტასკები
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 p-2">
              {tasks.map((task) => (
                <div
                  key={task.id}
                  className="border rounded-xl p-4 hover:shadow-xl transition-all duration-300 bg-card hover:scale-[1.02] group cursor-pointer hover:border-primary/50 backdrop-blur-sm"
                >
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="font-semibold text-base leading-tight group-hover:text-primary transition-colors">
                      ტასკი #{task.id}
                    </h3>
                    {getStatusBadge(task.status_id)}
                  </div>

                  {task.task_description && (
                    <p className="text-muted-foreground mb-3 leading-relaxed text-sm line-clamp-3">
                      {task.task_description}
                    </p>
                  )}

                  <div className="space-y-2">
                    {task.created_at && (
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Calendar className="w-3 h-3" />
                        <span>შექმნილია: {formatDate(task.created_at)}</span>
                      </div>
                    )}

                    {task.end_date && (
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="w-3 h-3" />
                        <span>ვადა: {formatDate(task.end_date)}</span>
                      </div>
                    )}

                    <div className="flex flex-wrap gap-1">
                      {task.type && (
                        <Badge variant="outline" className="text-xs">
                          ტიპი: {task.type}
                        </Badge>
                      )}

                      {task.rate && (
                        <Badge variant="outline" className="text-xs">
                          შეფასება: {task.rate}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {renderSliderPagination()}
      </DialogContent>
    </Dialog>
  )
}
