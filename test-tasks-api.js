// Test script for tasks API
const API_BASE_URL = 'http://*************:8000/api'

async function testTasksAPI() {
  try {
    // Test the tasks endpoint
    const response = await fetch(`${API_BASE_URL}/node/1/tasks?perPage=10&page=1`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': 'Bearer YOUR_TOKEN_HERE' // Replace with actual token
      }
    })

    const data = await response.json()
    console.log('Tasks API Response:', data)
    
    if (data.success && data.data) {
      console.log('✅ Tasks API is working!')
      console.log('Tasks data:', data.data.data)
      console.log('Pagination info:', {
        current_page: data.data.data.current_page,
        last_page: data.data.data.last_page,
        total: data.data.data.total
      })
    } else {
      console.log('❌ Tasks API returned unexpected format')
    }
  } catch (error) {
    console.error('❌ Error testing tasks API:', error)
  }
}

// Run the test
testTasksAPI()
