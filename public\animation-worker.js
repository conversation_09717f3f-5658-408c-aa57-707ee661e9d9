// Web Worker for animation calculations
// This runs on a separate thread to avoid blocking the main UI thread

let particles = [];
let animationConfig = {};
let isRunning = false;

// Bezier curve calculation function
function getBezierPoint(t, localSourceX, localSourceY, localTargetX, localTargetY, controlPointOffset) {
  const oneMinusT = 1 - t;
  const tSquared = t * t;
  const oneMinusTSquared = oneMinusT * oneMinusT;
  
  const x = oneMinusTSquared * localSourceX + 
            2 * oneMinusT * t * localSourceX + 
            tSquared * localTargetX;
  
  const y = oneMinusTSquared * localSourceY + 
            2 * oneMinusT * t * (localSourceY + controlPointOffset) + 
            tSquared * localTargetY;

  return { x, y };
}

// Animation loop
function animate() {
  if (!isRunning) return;

  const currentTime = performance.now();
  const deltaTime = currentTime - animationConfig.lastTime;
  animationConfig.lastTime = currentTime;

  const updatedParticles = particles.map(particle => {
    // Update particle position
    particle.offset += particle.speed * deltaTime;
    if (particle.offset > animationConfig.approximateLength) {
      particle.offset = 0;
    }

    const t = particle.offset / animationConfig.approximateLength;
    const point = getBezierPoint(
      t, 
      animationConfig.localSourceX, 
      animationConfig.localSourceY, 
      animationConfig.localTargetX, 
      animationConfig.localTargetY, 
      animationConfig.controlPointOffset
    );

    return {
      ...particle,
      x: point.x,
      y: point.y,
      scale: 0.8 + Math.sin(t * Math.PI * 2) * 0.1
    };
  });

  // Send updated particle positions back to main thread
  self.postMessage({
    type: 'PARTICLES_UPDATE',
    particles: updatedParticles
  });

  // Schedule next frame (30 FPS)
  setTimeout(animate, 33.33);
}

// Message handler
self.onmessage = function(e) {
  const { type, data } = e.data;

  switch (type) {
    case 'INIT':
      animationConfig = {
        ...data,
        lastTime: performance.now()
      };
      
      // Initialize particles
      particles = [];
      const particleCount = 1; // Single particle for optimal performance
      
      for (let i = 0; i < particleCount; i++) {
        particles.push({
          offset: (i / particleCount) * data.approximateLength,
          speed: data.approximateLength / data.duration,
          x: data.localSourceX,
          y: data.localSourceY,
          scale: 0.8
        });
      }
      break;

    case 'START':
      isRunning = true;
      animationConfig.lastTime = performance.now();
      animate();
      break;

    case 'STOP':
      isRunning = false;
      break;

    case 'UPDATE_CONFIG':
      animationConfig = {
        ...animationConfig,
        ...data
      };
      break;

    default:
      console.warn('Unknown message type:', type);
  }
};
